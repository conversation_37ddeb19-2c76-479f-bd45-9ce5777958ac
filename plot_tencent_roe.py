#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
腾讯ROE趋势图绘制脚本
"""

import sqlite3
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime
import numpy as np

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def plot_tencent_roe(db_name='ganggutong_financial_data.db'):
    """绘制腾讯ROE趋势图"""
    
    # 从数据库获取腾讯ROE数据
    conn = sqlite3.connect(db_name)
    
    query = """
    SELECT report_date, 
           roe*100 as roe_percent, 
           net_income/1e12 as net_income_trillion,
           shareholders_equity/1e12 as equity_trillion
    FROM financial_data 
    WHERE stock_code = '00700' AND roe IS NOT NULL 
    ORDER BY report_date
    """
    
    df = pd.read_sql_query(query, conn)
    conn.close()
    
    # 转换日期格式
    df['report_date'] = pd.to_datetime(df['report_date'])
    
    # 创建图表
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(14, 10))
    fig.suptitle('腾讯控股 (00700) ROE分析', fontsize=16, fontweight='bold')
    
    # 第一个子图：ROE趋势
    ax1.plot(df['report_date'], df['roe_percent'], 
             marker='o', linewidth=2, markersize=6, 
             color='#1f77b4', markerfacecolor='white', 
             markeredgecolor='#1f77b4', markeredgewidth=2)
    
    ax1.set_title('ROE (净资产收益率) 趋势', fontsize=14, fontweight='bold')
    ax1.set_ylabel('ROE (%)', fontsize=12)
    ax1.grid(True, alpha=0.3)
    ax1.set_ylim(0, max(df['roe_percent']) * 1.1)
    
    # 添加ROE水平线
    ax1.axhline(y=15, color='green', linestyle='--', alpha=0.7, label='优秀线 (15%)')
    ax1.axhline(y=10, color='orange', linestyle='--', alpha=0.7, label='良好线 (10%)')
    ax1.legend()
    
    # 第二个子图：净利润和股东权益
    ax2_twin = ax2.twinx()
    
    line1 = ax2.plot(df['report_date'], df['net_income_trillion'], 
                     marker='s', linewidth=2, markersize=5, 
                     color='#2ca02c', label='净利润')
    
    line2 = ax2_twin.plot(df['report_date'], df['equity_trillion'], 
                          marker='^', linewidth=2, markersize=5, 
                          color='#ff7f0e', label='股东权益')
    
    ax2.set_title('净利润与股东权益趋势', fontsize=14, fontweight='bold')
    ax2.set_xlabel('报告日期', fontsize=12)
    ax2.set_ylabel('净利润 (万亿港元)', fontsize=12, color='#2ca02c')
    ax2_twin.set_ylabel('股东权益 (万亿港元)', fontsize=12, color='#ff7f0e')
    
    ax2.grid(True, alpha=0.3)
    ax2.tick_params(axis='y', labelcolor='#2ca02c')
    ax2_twin.tick_params(axis='y', labelcolor='#ff7f0e')
    
    # 合并图例
    lines1, labels1 = ax2.get_legend_handles_labels()
    lines2, labels2 = ax2_twin.get_legend_handles_labels()
    ax2.legend(lines1 + lines2, labels1 + labels2, loc='upper left')
    
    # 设置x轴日期格式
    for ax in [ax1, ax2]:
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
        ax.xaxis.set_major_locator(mdates.YearLocator(2))
        plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)
    
    plt.tight_layout()
    
    # 保存图表
    plt.savefig('tencent_roe_analysis.png', dpi=300, bbox_inches='tight')
    print("图表已保存为: tencent_roe_analysis.png")
    
    # 显示统计信息
    print("\n=== 腾讯ROE统计信息 ===")
    print(f"数据期间: {df['report_date'].min().strftime('%Y-%m-%d')} 到 {df['report_date'].max().strftime('%Y-%m-%d')}")
    print(f"平均ROE: {df['roe_percent'].mean():.2f}%")
    print(f"最高ROE: {df['roe_percent'].max():.2f}% ({df.loc[df['roe_percent'].idxmax(), 'report_date'].strftime('%Y-%m-%d')})")
    print(f"最低ROE: {df['roe_percent'].min():.2f}% ({df.loc[df['roe_percent'].idxmin(), 'report_date'].strftime('%Y-%m-%d')})")
    print(f"最新ROE: {df['roe_percent'].iloc[-1]:.2f}% ({df['report_date'].iloc[-1].strftime('%Y-%m-%d')})")
    
    # 近年来ROE趋势
    recent_data = df[df['report_date'] >= '2020-01-01']
    if len(recent_data) > 0:
        print(f"\n=== 近年来ROE表现 (2020年以来) ===")
        print(f"平均ROE: {recent_data['roe_percent'].mean():.2f}%")
        print(f"ROE波动性 (标准差): {recent_data['roe_percent'].std():.2f}%")
    
    plt.show()

if __name__ == "__main__":
    plot_tencent_roe()
