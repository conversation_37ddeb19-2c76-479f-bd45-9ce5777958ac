#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
添加ROE-TTM计算功能
ROE-TTM = 最近4个季度净利润总和 / 最新股东权益
"""

import sqlite3
import pandas as pd
import logging
from datetime import datetime, timedelta

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('roe_ttm_calculation.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ROETTMCalculator:
    """ROE-TTM计算器"""
    
    def __init__(self, db_name='ganggutong_financial_data.db'):
        self.db_name = db_name
        
    def add_roe_ttm_column(self):
        """添加roe_ttm字段到数据库"""
        try:
            conn = sqlite3.connect(self.db_name)
            cursor = conn.cursor()
            
            # 检查是否已存在roe_ttm字段
            cursor.execute("PRAGMA table_info(financial_data)")
            columns = [col[1] for col in cursor.fetchall()]
            
            if 'roe_ttm' not in columns:
                cursor.execute("ALTER TABLE financial_data ADD COLUMN roe_ttm REAL")
                conn.commit()
                logger.info("✅ 成功添加roe_ttm字段")
            else:
                logger.info("roe_ttm字段已存在")
            
            conn.close()
            
        except Exception as e:
            logger.error(f"添加roe_ttm字段失败: {e}")
    
    def calculate_roe_ttm_for_stock(self, stock_code):
        """计算单个股票的ROE-TTM"""
        try:
            conn = sqlite3.connect(self.db_name)
            
            # 获取该股票的所有季度数据，按日期排序
            query = """
            SELECT report_date, net_income, shareholders_equity, roe
            FROM financial_data 
            WHERE stock_code = ? AND period_type = 'quarterly'
            AND net_income IS NOT NULL AND shareholders_equity IS NOT NULL
            ORDER BY report_date DESC
            """
            
            df = pd.read_sql_query(query, conn, params=(stock_code,))
            
            if len(df) < 4:
                logger.warning(f"{stock_code}: 数据不足4个季度，无法计算ROE-TTM")
                conn.close()
                return
            
            # 转换日期
            df['report_date'] = pd.to_datetime(df['report_date'])
            
            # 按报告期分组计算ROE-TTM
            roe_ttm_updates = []
            
            for i in range(len(df)):
                current_date = df.iloc[i]['report_date']
                current_equity = df.iloc[i]['shareholders_equity']
                
                # 获取最近4个季度的净利润（包括当前季度）
                recent_4_quarters = df.iloc[i:i+4]
                
                if len(recent_4_quarters) == 4:
                    # 计算最近4个季度净利润总和
                    ttm_net_income = recent_4_quarters['net_income'].sum()
                    
                    # 计算ROE-TTM
                    if current_equity and current_equity != 0:
                        roe_ttm = ttm_net_income / current_equity
                        
                        # ROE-TTM合理性检查
                        if -2 <= roe_ttm <= 2:  # ROE-TTM范围可以比单季度ROE更宽
                            roe_ttm_updates.append({
                                'report_date': current_date.strftime('%Y-%m-%d'),
                                'roe_ttm': roe_ttm,
                                'ttm_net_income': ttm_net_income,
                                'quarters_used': recent_4_quarters['report_date'].dt.strftime('%Y-%m-%d').tolist()
                            })
                            
                            logger.debug(f"{stock_code} {current_date.strftime('%Y-%m-%d')}: "
                                       f"ROE-TTM = {roe_ttm*100:.2f}% "
                                       f"(TTM净利润: {ttm_net_income/1e9:.1f}十亿, "
                                       f"股东权益: {current_equity/1e12:.3f}万亿)")
                        else:
                            logger.warning(f"{stock_code} {current_date.strftime('%Y-%m-%d')}: "
                                         f"ROE-TTM异常 {roe_ttm*100:.2f}%，跳过")
            
            # 批量更新数据库
            cursor = conn.cursor()
            for update in roe_ttm_updates:
                cursor.execute("""
                    UPDATE financial_data 
                    SET roe_ttm = ?
                    WHERE stock_code = ? AND report_date = ?
                """, (update['roe_ttm'], stock_code, update['report_date']))
            
            conn.commit()
            conn.close()
            
            logger.info(f"✅ {stock_code}: 成功计算 {len(roe_ttm_updates)} 个ROE-TTM值")
            
            return roe_ttm_updates
            
        except Exception as e:
            logger.error(f"计算{stock_code}的ROE-TTM失败: {e}")
            if 'conn' in locals():
                conn.close()
            return []
    
    def calculate_all_roe_ttm(self):
        """计算所有股票的ROE-TTM"""
        try:
            conn = sqlite3.connect(self.db_name)
            
            # 获取所有有财务数据的股票
            query = """
            SELECT DISTINCT stock_code 
            FROM financial_data 
            WHERE net_income IS NOT NULL AND shareholders_equity IS NOT NULL
            ORDER BY stock_code
            """
            
            stock_codes = pd.read_sql_query(query, conn)['stock_code'].tolist()
            conn.close()
            
            logger.info(f"开始计算 {len(stock_codes)} 只股票的ROE-TTM")
            
            total_updated = 0
            for i, stock_code in enumerate(stock_codes, 1):
                logger.info(f"[{i}/{len(stock_codes)}] 处理 {stock_code}")
                updates = self.calculate_roe_ttm_for_stock(stock_code)
                total_updated += len(updates) if updates else 0
            
            logger.info(f"✅ 完成所有股票ROE-TTM计算，总共更新 {total_updated} 条记录")
            
        except Exception as e:
            logger.error(f"批量计算ROE-TTM失败: {e}")
    
    def verify_roe_ttm_calculation(self, stock_code='00700', limit=5):
        """验证ROE-TTM计算结果"""
        try:
            conn = sqlite3.connect(self.db_name)
            
            query = """
            SELECT report_date, 
                   net_income/1e9 as income_billion,
                   shareholders_equity/1e12 as equity_trillion,
                   roe*100 as roe_percent,
                   roe_ttm*100 as roe_ttm_percent
            FROM financial_data 
            WHERE stock_code = ? AND roe_ttm IS NOT NULL
            ORDER BY report_date DESC
            LIMIT ?
            """
            
            df = pd.read_sql_query(query, conn, params=(stock_code, limit))
            conn.close()
            
            logger.info(f"=== {stock_code} ROE vs ROE-TTM 对比 ===")
            for _, row in df.iterrows():
                logger.info(f"报告期: {row['report_date']}")
                logger.info(f"  净利润: {row['income_billion']:.1f} 十亿元")
                logger.info(f"  股东权益: {row['equity_trillion']:.3f} 万亿元")
                logger.info(f"  ROE(单季): {row['roe_percent']:.2f}%")
                logger.info(f"  ROE-TTM: {row['roe_ttm_percent']:.2f}%")
                logger.info("-" * 40)
            
        except Exception as e:
            logger.error(f"验证ROE-TTM计算失败: {e}")
    
    def get_ttm_details(self, stock_code, report_date):
        """获取特定日期的TTM计算详情"""
        try:
            conn = sqlite3.connect(self.db_name)
            
            # 获取该日期及之前3个季度的数据
            query = """
            SELECT report_date, net_income/1e9 as income_billion
            FROM financial_data 
            WHERE stock_code = ? AND report_date <= ?
            AND net_income IS NOT NULL
            ORDER BY report_date DESC
            LIMIT 4
            """
            
            df = pd.read_sql_query(query, conn, params=(stock_code, report_date))
            conn.close()
            
            if len(df) == 4:
                logger.info(f"=== {stock_code} {report_date} TTM计算详情 ===")
                total_income = 0
                for _, row in df.iterrows():
                    logger.info(f"{row['report_date']}: {row['income_billion']:.1f} 十亿元")
                    total_income += row['income_billion']
                logger.info(f"TTM净利润总和: {total_income:.1f} 十亿元")
            else:
                logger.warning(f"{stock_code} {report_date}: 数据不足4个季度")
            
        except Exception as e:
            logger.error(f"获取TTM详情失败: {e}")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='计算ROE-TTM')
    parser.add_argument('--db-name', default='ganggutong_financial_data.db', help='数据库文件名')
    parser.add_argument('--stock-code', help='指定股票代码（可选）')
    parser.add_argument('--verify', action='store_true', help='验证计算结果')
    parser.add_argument('--details', help='查看指定日期的TTM计算详情，格式：股票代码,日期')
    
    args = parser.parse_args()
    
    calculator = ROETTMCalculator(args.db_name)
    
    if args.details:
        stock_code, report_date = args.details.split(',')
        calculator.get_ttm_details(stock_code.strip(), report_date.strip())
    elif args.verify:
        stock_code = args.stock_code or '00700'
        calculator.verify_roe_ttm_calculation(stock_code)
    else:
        # 添加字段
        calculator.add_roe_ttm_column()
        
        # 计算ROE-TTM
        if args.stock_code:
            calculator.calculate_roe_ttm_for_stock(args.stock_code)
            calculator.verify_roe_ttm_calculation(args.stock_code)
        else:
            calculator.calculate_all_roe_ttm()
            calculator.verify_roe_ttm_calculation('00700')  # 验证腾讯的结果

if __name__ == "__main__":
    main()
