#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
港股通成分股财务数据下载器
从多个数据源获取港股通成分股的财务数据并存储到SQLite数据库中
"""

import os
import sqlite3
import pandas as pd
import akshare as ak
import requests
from datetime import datetime, timedelta
import time
import logging
from tqdm import tqdm
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
import json
import warnings

# 忽略警告
warnings.filterwarnings('ignore')

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('ganggutong_financial_download.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class GangguTongFinancialDownloader:
    """港股通财务数据下载器"""
    
    def __init__(self, db_name='ganggutong_financial_data.db', csv_file='data_files/ganggutong_consti.csv'):
        self.db_name = db_name
        self.csv_file = csv_file
        self.max_workers = 3  # 并发线程数
        self.request_delay = 1  # 请求间隔（秒）
        self.retry_attempts = 3  # 重试次数
        self.db_lock = threading.Lock()
        
        # 初始化数据库
        self.init_database()
        
    def init_database(self):
        """初始化SQLite数据库"""
        try:
            conn = sqlite3.connect(self.db_name)
            cursor = conn.cursor()
            
            # 创建股票基本信息表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS stock_info (
                    stock_code TEXT PRIMARY KEY,
                    stock_name TEXT NOT NULL,
                    industry TEXT,
                    market TEXT DEFAULT 'HK',
                    yahoo_symbol TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 创建财务数据表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS financial_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    stock_code TEXT NOT NULL,
                    report_date TEXT NOT NULL,
                    period_type TEXT NOT NULL,  -- 'annual' or 'quarterly'
                    revenue REAL,
                    gross_profit REAL,
                    operating_income REAL,
                    net_income REAL,
                    total_assets REAL,
                    total_debt REAL,
                    shareholders_equity REAL,
                    operating_cash_flow REAL,
                    free_cash_flow REAL,
                    eps REAL,
                    book_value_per_share REAL,
                    roe REAL,
                    roa REAL,
                    debt_to_equity REAL,
                    current_ratio REAL,
                    gross_margin REAL,
                    operating_margin REAL,
                    net_margin REAL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(stock_code, report_date, period_type),
                    FOREIGN KEY (stock_code) REFERENCES stock_info (stock_code)
                )
            ''')
            
            # 创建价格数据表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS price_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    stock_code TEXT NOT NULL,
                    date TEXT NOT NULL,
                    open REAL,
                    high REAL,
                    low REAL,
                    close REAL,
                    adj_close REAL,
                    volume INTEGER,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(stock_code, date),
                    FOREIGN KEY (stock_code) REFERENCES stock_info (stock_code)
                )
            ''')
            
            # 创建下载日志表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS download_log (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    stock_code TEXT NOT NULL,
                    data_type TEXT NOT NULL,  -- 'financial' or 'price'
                    status TEXT NOT NULL,     -- 'success' or 'failed'
                    error_message TEXT,
                    download_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (stock_code) REFERENCES stock_info (stock_code)
                )
            ''')
            
            # 创建索引
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_financial_data_code_date ON financial_data(stock_code, report_date)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_price_data_code_date ON price_data(stock_code, date)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_download_log_code_type ON download_log(stock_code, data_type)')
            
            conn.commit()
            conn.close()
            logger.info(f"数据库 {self.db_name} 初始化完成")
            
        except Exception as e:
            logger.error(f"数据库初始化失败: {e}")
            raise
    
    def load_stock_list(self):
        """从CSV文件加载股票列表"""
        try:
            df = pd.read_csv(self.csv_file, encoding='utf-8')
            stocks = []
            
            for _, row in df.iterrows():
                stock_code = str(row['代码']).zfill(5)  # 确保股票代码为5位数
                stock_name = row['名称']
                industry = row.get('所属行业', '')
                
                # 不再需要Yahoo Finance符号
                yahoo_symbol = None
                
                stocks.append({
                    'stock_code': stock_code,
                    'stock_name': stock_name,
                    'industry': industry,
                    'yahoo_symbol': yahoo_symbol
                })
            
            logger.info(f"成功加载 {len(stocks)} 只港股通成分股")
            return stocks
            
        except Exception as e:
            logger.error(f"加载股票列表失败: {e}")
            raise
    
    def save_stock_info(self, stocks):
        """保存股票基本信息到数据库"""
        try:
            with self.db_lock:
                conn = sqlite3.connect(self.db_name)
                cursor = conn.cursor()
                
                for stock in stocks:
                    cursor.execute('''
                        INSERT OR REPLACE INTO stock_info 
                        (stock_code, stock_name, industry, yahoo_symbol, updated_at)
                        VALUES (?, ?, ?, ?, ?)
                    ''', (
                        stock['stock_code'],
                        stock['stock_name'],
                        stock['industry'],
                        stock['yahoo_symbol'],
                        datetime.now().isoformat()
                    ))
                
                conn.commit()
                conn.close()
                logger.info(f"保存 {len(stocks)} 只股票基本信息到数据库")
                
        except Exception as e:
            logger.error(f"保存股票信息失败: {e}")
            raise
    


    def format_hk_stock_code(self, stock_code):
        """格式化港股代码为东方财富格式"""
        # 港股代码格式：保持原始代码 + .HK
        return f"{stock_code}.HK"

    def get_akshare_financial_data(self, stock_code, stock_name):
        """使用AKShare获取港股财务数据"""
        try:
            logger.info(f"使用AKShare获取 {stock_code} ({stock_name}) 的财务数据")

            # 尝试获取多种财务报表
            income_df = None
            balance_df = None
            indicator_df = None

            # 方法1: 获取利润表数据
            try:
                income_df = ak.stock_financial_hk_report_em(
                    stock=stock_code,
                    symbol="利润表",
                    indicator="报告期"
                )
                if income_df is not None and not income_df.empty:
                    logger.info(f"成功获取 {stock_code} 利润表数据，形状: {income_df.shape}")
            except Exception as e:
                logger.warning(f"获取 {stock_code} 利润表数据失败: {e}")

            # 方法2: 获取资产负债表数据
            try:
                balance_df = ak.stock_financial_hk_report_em(
                    stock=stock_code,
                    symbol="资产负债表",
                    indicator="报告期"
                )
                if balance_df is not None and not balance_df.empty:
                    logger.info(f"成功获取 {stock_code} 资产负债表数据，形状: {balance_df.shape}")
            except Exception as e:
                logger.warning(f"获取 {stock_code} 资产负债表数据失败: {e}")

            # 方法3: 获取财务指标数据
            try:
                indicator_df = ak.stock_financial_hk_analysis_indicator_em(
                    symbol=stock_code,
                    indicator="报告期"
                )
                if indicator_df is not None and not indicator_df.empty:
                    logger.info(f"成功获取 {stock_code} 财务指标数据，形状: {indicator_df.shape}")
            except Exception as e:
                logger.warning(f"获取 {stock_code} 财务指标数据失败: {e}")

            # 处理数据
            if income_df is not None or balance_df is not None or indicator_df is not None:
                return self.process_comprehensive_financial_data(income_df, balance_df, indicator_df, stock_code)
            else:
                logger.warning(f"AKShare无法获取 {stock_code} 的任何财务数据")
                return []

        except Exception as e:
            logger.warning(f"AKShare获取 {stock_code} 财务数据失败: {e}")
            return []

    def get_eastmoney_financial_data(self, stock_code, stock_name):
        """使用东方财富网API获取港股财务数据"""
        try:
            logger.info(f"使用东方财富网获取 {stock_code} ({stock_name}) 的财务数据")

            formatted_code = self.format_hk_stock_code(stock_code)

            # 构建API URL - 使用利润表接口（基于成功的实现）
            url = f"https://datacenter.eastmoney.com/securities/api/data/v1/get?reportName=RPT_HKF10_FN_INCOME_PC&columns=ALL&quoteColumns=&filter=(SECUCODE%3D%22{formatted_code}%22)&pageNumber=1&pageSize=200&sortTypes=-1&sortColumns=REPORT_DATE&source=F10&client=PC"

            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Referer': 'https://datacenter.eastmoney.com/',
                'Accept': 'application/json, text/javascript, */*; q=0.01',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
                'Cache-Control': 'no-cache',
                'Pragma': 'no-cache'
            }

            # 添加随机延迟避免被识别为爬虫
            import random
            time.sleep(random.uniform(1, 3))

            # 使用会话保持连接
            session = requests.Session()
            session.headers.update(headers)
            response = session.get(url, timeout=30)

            if response.status_code != 200:
                logger.warning(f"东方财富请求失败: {stock_code}, 状态码: {response.status_code}")
                return []

            try:
                data = response.json()
            except json.JSONDecodeError as e:
                logger.warning(f"东方财富响应解析失败: {stock_code}, 错误: {e}")
                return []

            # 检查响应结构
            if not isinstance(data, dict):
                logger.warning(f"东方财富响应格式错误: {stock_code}")
                return []

            # 检查响应是否成功
            if not data.get('success', False):
                logger.warning(f"东方财富API返回失败: {stock_code}, message: {data.get('message', 'Unknown error')}")
                return []

            if ('result' not in data or
                data['result'] is None or
                not isinstance(data['result'], dict) or
                'data' not in data['result'] or
                not data['result']['data']):
                logger.warning(f"东方财富无财务数据: {stock_code}")
                return []

            financial_data = data['result']['data']
            logger.info(f"东方财富获取到 {stock_code} 的 {len(financial_data)} 条财务记录")

            return self.process_eastmoney_financial_data(financial_data, stock_code)

        except Exception as e:
            logger.warning(f"东方财富获取 {stock_code} 财务数据失败: {e}")
            return []

    def process_comprehensive_financial_data(self, income_df, balance_df, indicator_df, stock_code):
        """综合处理多种财务报表数据"""
        try:
            records = []

            # 处理利润表数据
            if income_df is not None and not income_df.empty:
                logger.info(f"处理 {stock_code} 利润表数据，列名: {income_df.columns.tolist()}")

                # 检查数据结构 - 东方财富格式
                if 'STD_ITEM_NAME' in income_df.columns and 'REPORT_DATE' in income_df.columns and 'AMOUNT' in income_df.columns:
                    # 按报告日期分组
                    date_groups = income_df.groupby('REPORT_DATE')

                    for report_date, group in date_groups:
                        record = {
                            'stock_code': stock_code,
                            'report_date': str(report_date)[:10],
                            'period_type': 'quarterly'
                        }

                        # 提取利润表项目
                        for _, row in group.iterrows():
                            item_name = row.get('STD_ITEM_NAME', '')
                            amount = row.get('AMOUNT')

                            if pd.notna(amount) and amount != 0:
                                try:
                                    amount_value = float(amount)  # 东方财富API返回的数据已经是元为单位

                                    # 营业收入
                                    if any(keyword in item_name for keyword in ['营业收入', '营业额', '总收入', '收入总额', '营运收入']):
                                        if 'revenue' not in record:
                                            record['revenue'] = amount_value

                                    # 营业成本
                                    elif any(keyword in item_name for keyword in ['营业成本', '销售成本', '主营业务成本']):
                                        if 'cost_of_revenue' not in record:
                                            record['cost_of_revenue'] = amount_value

                                    # 毛利润
                                    elif any(keyword in item_name for keyword in ['毛利润', '毛利']):
                                        if 'gross_profit' not in record:
                                            record['gross_profit'] = amount_value

                                    # 营业利润 (港股使用"经营溢利"等术语)
                                    elif any(keyword in item_name for keyword in ['营业利润', '经营利润', '经营溢利', '营运收入']):
                                        if 'operating_income' not in record:
                                            record['operating_income'] = amount_value

                                    # 净利润 (港股使用"股东应占溢利"等术语)
                                    elif any(keyword in item_name for keyword in ['净利润', '归属于母公司', '归母净利润', '股东应占溢利', '除税后溢利', '持续经营业务税后利润']):
                                        if 'net_income' not in record:
                                            record['net_income'] = amount_value

                                except (ValueError, TypeError):
                                    continue

                        # 计算毛利润（如果没有直接数据）
                        if 'gross_profit' not in record and record.get('revenue') and record.get('cost_of_revenue'):
                            record['gross_profit'] = record['revenue'] - record['cost_of_revenue']

                        # 计算利润率
                        if record.get('revenue'):
                            if record.get('gross_profit'):
                                record['gross_margin'] = record['gross_profit'] / record['revenue']
                            if record.get('operating_income'):
                                record['operating_margin'] = record['operating_income'] / record['revenue']
                            if record.get('net_income'):
                                record['net_margin'] = record['net_income'] / record['revenue']

                        if len(record) > 3:  # 确保有实际的财务数据
                            records.append(record)

            # 处理资产负债表数据
            if balance_df is not None and not balance_df.empty:
                logger.info(f"处理 {stock_code} 资产负债表数据")

                if 'STD_ITEM_NAME' in balance_df.columns and 'REPORT_DATE' in balance_df.columns and 'AMOUNT' in balance_df.columns:
                    # 按报告日期分组
                    date_groups = balance_df.groupby('REPORT_DATE')

                    for report_date, group in date_groups:
                        report_date_str = str(report_date)[:10]

                        # 查找对应的记录
                        existing_record = None
                        for record in records:
                            if record['report_date'] == report_date_str:
                                existing_record = record
                                break

                        if existing_record is None:
                            existing_record = {
                                'stock_code': stock_code,
                                'report_date': report_date_str,
                                'period_type': 'quarterly'
                            }
                            records.append(existing_record)

                        # 提取资产负债表项目
                        for _, row in group.iterrows():
                            item_name = row.get('STD_ITEM_NAME', '')
                            amount = row.get('AMOUNT')

                            if pd.notna(amount) and amount != 0:
                                try:
                                    amount_value = float(amount)  # 东方财富API返回的数据已经是元为单位

                                    # 总资产
                                    if any(keyword in item_name for keyword in ['总资产', '资产总计']):
                                        if 'total_assets' not in existing_record:
                                            existing_record['total_assets'] = amount_value

                                    # 总负债
                                    elif any(keyword in item_name for keyword in ['总负债', '负债总计']):
                                        if 'total_debt' not in existing_record:
                                            existing_record['total_debt'] = amount_value

                                    # 股东权益 (港股使用"总权益"等术语)
                                    elif any(keyword in item_name for keyword in ['股东权益', '所有者权益', '净资产', '总权益', '总资产减总负债合计']):
                                        if 'shareholders_equity' not in existing_record:
                                            existing_record['shareholders_equity'] = amount_value

                                    # 流动资产
                                    elif any(keyword in item_name for keyword in ['流动资产']):
                                        if 'current_assets' not in existing_record:
                                            existing_record['current_assets'] = amount_value

                                    # 流动负债
                                    elif any(keyword in item_name for keyword in ['流动负债']):
                                        if 'current_liabilities' not in existing_record:
                                            existing_record['current_liabilities'] = amount_value

                                except (ValueError, TypeError):
                                    continue

                        # 计算财务比率
                        if existing_record.get('total_assets') and existing_record.get('net_income'):
                            existing_record['roa'] = existing_record['net_income'] / existing_record['total_assets']

                        if existing_record.get('shareholders_equity') and existing_record.get('net_income'):
                            roe_value = existing_record['net_income'] / existing_record['shareholders_equity']
                            # ROE数据验证：合理范围通常在-100%到100%之间
                            if -1 <= roe_value <= 1:
                                existing_record['roe'] = roe_value
                            else:
                                logger.warning(f"{stock_code} ROE异常: {roe_value:.4f}, 净利润: {existing_record['net_income']}, 股东权益: {existing_record['shareholders_equity']}")

                        if existing_record.get('total_assets') and existing_record.get('total_debt'):
                            existing_record['debt_to_equity'] = existing_record['total_debt'] / existing_record['total_assets']

                        if existing_record.get('current_assets') and existing_record.get('current_liabilities'):
                            existing_record['current_ratio'] = existing_record['current_assets'] / existing_record['current_liabilities']

            logger.info(f"成功处理 {stock_code} 综合财务数据，获得 {len(records)} 条记录")
            return records

        except Exception as e:
            logger.warning(f"处理 {stock_code} 综合财务数据失败: {e}")
            return []

    def process_akshare_financial_data(self, income_df, indicator_df, stock_code):
        """处理AKShare财务数据"""
        try:
            records = []

            # 处理利润表数据 - 基于实际的数据结构
            if income_df is not None and not income_df.empty:
                logger.info(f"处理 {stock_code} 利润表数据，列名: {income_df.columns.tolist()}")

                # 检查数据结构 - 这是东方财富格式的数据
                if 'STD_ITEM_NAME' in income_df.columns and 'REPORT_DATE' in income_df.columns and 'AMOUNT' in income_df.columns:
                    # 查找营收相关项目
                    revenue_keywords = ['营业收入', '营业额', '总收入', '收入总额', '营运收入', '经营收入总额']

                    for keyword in revenue_keywords:
                        revenue_rows = income_df[income_df['STD_ITEM_NAME'].str.contains(keyword, na=False)]
                        if not revenue_rows.empty:
                            logger.info(f"找到 {len(revenue_rows)} 条 {keyword} 记录")

                            # 按报告日期分组
                            date_groups = revenue_rows.groupby('REPORT_DATE')

                            for report_date, group in date_groups:
                                try:
                                    # 获取该日期的营收数据（取第一条，通常是主营业务收入）
                                    revenue_row = group.iloc[0]
                                    amount = revenue_row['AMOUNT']

                                    if pd.notna(amount) and amount != 0:
                                        record = {
                                            'stock_code': stock_code,
                                            'report_date': str(report_date)[:10],
                                            'period_type': 'quarterly',
                                            'revenue': float(amount) if amount else None  # 东方财富API返回的数据已经是元为单位
                                        }

                                        # 尝试提取其他财务指标
                                        date_data = income_df[income_df['REPORT_DATE'] == report_date]

                                        # 查找净利润 (港股使用"股东应占溢利"等术语)
                                        profit_keywords = ['净利润', '归属于母公司', '归母净利润', '股东应占溢利', '除税后溢利', '持续经营业务税后利润']
                                        for profit_keyword in profit_keywords:
                                            profit_rows = date_data[date_data['STD_ITEM_NAME'].str.contains(profit_keyword, na=False)]
                                            if not profit_rows.empty:
                                                profit_amount = profit_rows.iloc[0]['AMOUNT']
                                                if pd.notna(profit_amount):
                                                    record['net_income'] = float(profit_amount)  # 东方财富API返回的数据已经是元为单位
                                                break

                                        # 计算利润率
                                        if record.get('revenue') and record.get('net_income'):
                                            record['net_margin'] = record['net_income'] / record['revenue']

                                        records.append(record)

                                except Exception as e:
                                    logger.warning(f"处理 {stock_code} 报告期 {report_date} 数据时出错: {e}")
                                    continue

                            # 找到营收数据就退出循环
                            if records:
                                break

                else:
                    # 处理其他格式的数据
                    logger.info(f"尝试处理其他格式的 {stock_code} 数据")

                    # 如果有报表项目列，按行处理
                    if '报表项目' in income_df.columns:
                        revenue_keywords = ['营业收入', '营业额', '总收入', '收入']
                        for keyword in revenue_keywords:
                            revenue_rows = income_df[income_df['报表项目'].str.contains(keyword, na=False)]
                            if not revenue_rows.empty:
                                revenue_row = revenue_rows.iloc[0]

                                # 遍历每一列（每一个报告期）
                                for col in income_df.columns:
                                    if col == '报表项目':
                                        continue

                                    try:
                                        # 尝试将列名转换为日期
                                        report_date = pd.to_datetime(col)
                                        revenue = revenue_row[col]

                                        if pd.notna(revenue) and revenue != 0:
                                            if isinstance(revenue, str):
                                                revenue = float(revenue.replace(',', '').replace(' ', ''))

                                            record = {
                                                'stock_code': stock_code,
                                                'report_date': report_date.strftime('%Y-%m-%d'),
                                                'period_type': 'quarterly',
                                                'revenue': float(revenue) if revenue else None  # 根据实际数据源确定是否需要单位转换
                                            }

                                            if record['revenue']:
                                                records.append(record)

                                    except Exception as e:
                                        continue

                                if records:
                                    break

            logger.info(f"成功处理 {stock_code} 数据，获得 {len(records)} 条记录")
            return records

        except Exception as e:
            logger.warning(f"处理AKShare {stock_code} 财务数据失败: {e}")
            return []

    def process_eastmoney_financial_data(self, financial_data, stock_code):
        """处理东方财富财务数据"""
        try:
            records = []

            # 按报告日期分组
            date_groups = {}
            for item in financial_data:
                report_date = item.get('REPORT_DATE', '')[:10]
                if report_date not in date_groups:
                    date_groups[report_date] = []
                date_groups[report_date].append(item)

            # 处理每个报告期的数据
            for report_date, items in date_groups.items():
                record = {
                    'stock_code': stock_code,
                    'report_date': report_date,
                    'period_type': 'quarterly'
                }

                # 查找营收数据
                for item in items:
                    item_name = item.get('STD_ITEM_NAME', '')
                    amount = item.get('AMOUNT')

                    if amount and pd.notna(amount):
                        try:
                            amount_value = float(amount)

                            # 营收相关项目
                            if any(keyword in item_name for keyword in ['营业额', '营运收入', '经营收入总额', '营业收入', '收入总额']):
                                if 'revenue' not in record or '营业额' in item_name:  # 优先使用营业额
                                    record['revenue'] = amount_value  # API返回的已经是元

                            # 营业利润相关项目 (港股使用"经营溢利"等术语)
                            elif any(keyword in item_name for keyword in ['营业利润', '经营利润', '经营溢利', '营运收入']):
                                if 'operating_income' not in record:
                                    record['operating_income'] = amount_value  # API返回的已经是元

                            # 净利润相关项目 (港股使用"股东应占溢利"等术语)
                            elif any(keyword in item_name for keyword in ['净利润', '归属于母公司', '归母净利润', '股东应占溢利', '除税后溢利', '持续经营业务税后利润']):
                                if 'net_income' not in record:
                                    record['net_income'] = amount_value  # API返回的已经是元

                        except (ValueError, TypeError):
                            continue

                # 计算利润率
                if record.get('revenue') and record.get('net_income'):
                    record['net_margin'] = record['net_income'] / record['revenue']

                if len(record) > 3:  # 确保有实际的财务数据
                    records.append(record)

            return records

        except Exception as e:
            logger.warning(f"处理东方财富 {stock_code} 财务数据失败: {e}")
            return []
    


    def save_financial_data(self, financial_records):
        """保存财务数据到数据库"""
        if not financial_records:
            return

        try:
            with self.db_lock:
                conn = sqlite3.connect(self.db_name)
                cursor = conn.cursor()

                for record in financial_records:
                    cursor.execute('''
                        INSERT OR REPLACE INTO financial_data
                        (stock_code, report_date, period_type, revenue, gross_profit,
                         operating_income, net_income, total_assets, total_debt,
                         shareholders_equity, operating_cash_flow, free_cash_flow,
                         eps, book_value_per_share, roe, roa, debt_to_equity,
                         current_ratio, gross_margin, operating_margin, net_margin)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        record['stock_code'], record['report_date'], record['period_type'],
                        record.get('revenue'), record.get('gross_profit'),
                        record.get('operating_income'), record.get('net_income'),
                        record.get('total_assets'), record.get('total_debt'),
                        record.get('shareholders_equity'), record.get('operating_cash_flow'),
                        record.get('free_cash_flow'), record.get('eps'),
                        record.get('book_value_per_share'), record.get('roe'),
                        record.get('roa'), record.get('debt_to_equity'),
                        record.get('current_ratio'), record.get('gross_margin'),
                        record.get('operating_margin'), record.get('net_margin')
                    ))

                conn.commit()
                conn.close()

        except Exception as e:
            logger.error(f"保存财务数据失败: {e}")



    def log_download_status(self, stock_code, data_type, status, error_message=None):
        """记录下载状态"""
        try:
            with self.db_lock:
                conn = sqlite3.connect(self.db_name)
                cursor = conn.cursor()

                cursor.execute('''
                    INSERT INTO download_log
                    (stock_code, data_type, status, error_message)
                    VALUES (?, ?, ?, ?)
                ''', (stock_code, data_type, status, error_message))

                conn.commit()
                conn.close()

        except Exception as e:
            logger.error(f"记录下载日志失败: {e}")

    def download_stock_data(self, stock):
        """下载单只股票的财务数据"""
        stock_code = stock['stock_code']
        stock_name = stock['stock_name']

        try:
            financial_records = []

            # 首先尝试使用AKShare获取数据
            financial_records = self.get_akshare_financial_data(stock_code, stock_name)

            # 如果AKShare失败，尝试东方财富网
            if not financial_records:
                logger.info(f"AKShare失败，尝试东方财富网获取 {stock_code} 数据")
                financial_records = self.get_eastmoney_financial_data(stock_code, stock_name)

            # 保存数据
            if financial_records:
                self.save_financial_data(financial_records)
                self.log_download_status(stock_code, 'financial', 'success')
                logger.info(f"✅ 成功下载 {stock_code} ({stock_name}) 财务数据，共 {len(financial_records)} 条记录")
            else:
                self.log_download_status(stock_code, 'financial', 'failed', '所有数据源都无法获取财务数据')
                logger.warning(f"❌ {stock_code} ({stock_name}) 无法获取财务数据")

            # 请求间隔
            time.sleep(self.request_delay)

        except Exception as e:
            error_msg = str(e)
            self.log_download_status(stock_code, 'financial', 'failed', error_msg)
            logger.error(f"❌ 下载 {stock_code} ({stock_name}) 财务数据失败: {error_msg}")

    def get_downloaded_stocks(self):
        """获取已下载财务数据的股票列表"""
        try:
            conn = sqlite3.connect(self.db_name)
            cursor = conn.cursor()

            # 获取已有财务数据的股票代码
            cursor.execute("SELECT DISTINCT stock_code FROM financial_data")
            downloaded_stocks = {row[0] for row in cursor.fetchall()}

            conn.close()
            return downloaded_stocks

        except Exception as e:
            logger.warning(f"获取已下载股票列表失败: {e}")
            return set()

    def download_all_data(self, max_stocks=None, start_from_famous=True, skip_downloaded=True):
        """下载所有股票数据"""
        try:
            # 加载股票列表
            stocks = self.load_stock_list()

            # 获取已下载的股票列表
            downloaded_stocks = set()
            if skip_downloaded:
                downloaded_stocks = self.get_downloaded_stocks()
                logger.info(f"已下载财务数据的股票数量: {len(downloaded_stocks)}")

            # 过滤掉已下载的股票
            if skip_downloaded and downloaded_stocks:
                original_count = len(stocks)
                stocks = [stock for stock in stocks if stock['stock_code'] not in downloaded_stocks]
                skipped_count = original_count - len(stocks)
                logger.info(f"跳过已下载的 {skipped_count} 只股票，剩余 {len(stocks)} 只股票需要下载")

            # 如果没有股票需要下载
            if not stocks:
                logger.info("所有股票的财务数据都已下载完成！")
                return

            # 如果启用从知名股票开始，重新排序
            if start_from_famous:
                famous_stocks = ['00005', '00700', '00939', '01398', '03988', '09988']
                famous_first = []
                others = []

                for stock in stocks:
                    if stock['stock_code'] in famous_stocks:
                        famous_first.append(stock)
                    else:
                        others.append(stock)

                stocks = famous_first + others
                if famous_first:
                    logger.info(f"优先下载知名股票: {[s['stock_code'] for s in famous_first]}")

            if max_stocks:
                stocks = stocks[:max_stocks]
                logger.info(f"限制下载前 {max_stocks} 只股票")

            # 保存股票基本信息
            self.save_stock_info(stocks)

            # 使用线程池并发下载
            logger.info(f"开始下载 {len(stocks)} 只股票的财务数据...")

            with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                # 提交所有任务
                future_to_stock = {
                    executor.submit(self.download_stock_data, stock): stock
                    for stock in stocks
                }

                # 使用tqdm显示进度
                with tqdm(total=len(stocks), desc="下载进度") as pbar:
                    for future in as_completed(future_to_stock):
                        stock = future_to_stock[future]
                        try:
                            future.result()
                        except Exception as e:
                            logger.error(f"处理股票 {stock['stock_code']} 时发生错误: {e}")
                        finally:
                            pbar.update(1)

            logger.info("所有股票数据下载完成！")

        except Exception as e:
            logger.error(f"批量下载失败: {e}")
            raise

    def get_download_statistics(self):
        """获取下载统计信息"""
        try:
            conn = sqlite3.connect(self.db_name)
            cursor = conn.cursor()

            # 统计股票数量
            cursor.execute("SELECT COUNT(*) FROM stock_info")
            total_stocks = cursor.fetchone()[0]

            # 统计财务数据记录数
            cursor.execute("SELECT COUNT(*) FROM financial_data")
            financial_records = cursor.fetchone()[0]

            # 统计财务数据记录数
            cursor.execute("SELECT COUNT(*) FROM financial_data")
            financial_records = cursor.fetchone()[0]

            # 统计成功下载财务数据的股票数
            cursor.execute("""
                SELECT COUNT(DISTINCT stock_code)
                FROM download_log
                WHERE status = 'success' AND data_type = 'financial'
            """)
            successful_financial = cursor.fetchone()[0]

            # 统计失败下载财务数据的股票数
            cursor.execute("""
                SELECT COUNT(DISTINCT stock_code)
                FROM download_log
                WHERE status = 'failed' AND data_type = 'financial'
            """)
            failed_financial = cursor.fetchone()[0]

            conn.close()

            stats = {
                'total_stocks': total_stocks,
                'financial_records': financial_records,
                'successful_financial': successful_financial,
                'failed_financial': failed_financial
            }

            return stats

        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
            return {}

    def print_statistics(self):
        """打印下载统计信息"""
        stats = self.get_download_statistics()

        print("\n" + "="*50)
        print("港股通财务数据下载统计")
        print("="*50)
        print(f"总股票数量: {stats.get('total_stocks', 0)}")
        print(f"财务数据记录数: {stats.get('financial_records', 0)}")
        print(f"成功下载财务数据的股票: {stats.get('successful_financial', 0)}")
        print(f"财务数据下载失败的股票: {stats.get('failed_financial', 0)}")
        success_rate = 0
        if stats.get('total_stocks', 0) > 0:
            success_rate = (stats.get('successful_financial', 0) / stats.get('total_stocks', 0)) * 100
        print(f"成功率: {success_rate:.1f}%")
        print("="*50)

    def export_to_csv(self, table_name, output_file=None):
        """导出数据到CSV文件"""
        try:
            if output_file is None:
                output_file = f"{table_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"

            conn = sqlite3.connect(self.db_name)
            df = pd.read_sql_query(f"SELECT * FROM {table_name}", conn)
            conn.close()

            df.to_csv(output_file, index=False, encoding='utf-8-sig')
            logger.info(f"成功导出 {table_name} 数据到 {output_file}")

        except Exception as e:
            logger.error(f"导出数据失败: {e}")


def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='港股通成分股财务数据下载器')
    parser.add_argument('--max-stocks', type=int, help='限制下载的股票数量（用于测试）')
    parser.add_argument('--no-skip', action='store_true', help='不跳过已下载的股票，重新下载所有数据')
    parser.add_argument('--stats', action='store_true', help='显示下载统计信息')
    parser.add_argument('--export', choices=['stock_info', 'financial_data'],
                       help='导出指定表的数据到CSV文件')
    parser.add_argument('--db-name', default='ganggutong_financial_data.db',
                       help='数据库文件名')
    parser.add_argument('--csv-file', default='data_files/ganggutong_consti.csv',
                       help='港股通成分股CSV文件路径')

    args = parser.parse_args()

    try:
        downloader = GangguTongFinancialDownloader(
            db_name=args.db_name,
            csv_file=args.csv_file
        )

        if args.stats:
            downloader.print_statistics()
        elif args.export:
            downloader.export_to_csv(args.export)
        else:
            # 检查基本依赖
            try:
                import requests
                import pandas
            except ImportError as e:
                print(f"缺少依赖包: {e}")
                print("请运行: pip install requests pandas tqdm")
                return

            # 开始下载数据
            skip_downloaded = not args.no_skip  # 如果指定了--no-skip，则不跳过已下载的股票
            downloader.download_all_data(max_stocks=args.max_stocks, skip_downloaded=skip_downloaded)
            downloader.print_statistics()

    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        raise


if __name__ == "__main__":
    main()
