#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析东方财富网API返回的字段，找出净利润的正确字段名
"""

import requests
import json
from collections import Counter
import pandas as pd

def analyze_api_fields():
    """分析API返回的所有字段"""
    print("🔍 分析东方财富网API字段...")
    
    # 测试几只不同的股票
    test_stocks = ["00700", "00941", "01299", "02318", "03690"]  # 腾讯、中国移动、友邦保险、平安、美团
    
    all_field_names = []
    
    for stock_code in test_stocks:
        print(f"\n📊 分析股票 {stock_code}...")
        formatted_code = f"{stock_code}.HK"
        
        url = f"https://datacenter.eastmoney.com/securities/api/data/v1/get?reportName=RPT_HKF10_FN_INCOME_PC&columns=ALL&quoteColumns=&filter=(SECUCODE%3D%22{formatted_code}%22)&pageNumber=1&pageSize=200&sortTypes=-1&sortColumns=REPORT_DATE&source=F10&client=PC"
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Referer': 'https://datacenter.eastmoney.com/',
            'Accept': 'application/json, text/javascript, */*; q=0.01'
        }
        
        try:
            response = requests.get(url, headers=headers, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                
                if 'result' in data and 'data' in data['result']:
                    records = data['result']['data']
                    print(f"   获取到 {len(records)} 条记录")
                    
                    # 收集所有字段名
                    stock_fields = [record.get('STD_ITEM_NAME', '') for record in records]
                    all_field_names.extend(stock_fields)
                    
                    # 查找可能的净利润字段
                    profit_related = []
                    for record in records:
                        item_name = record.get('STD_ITEM_NAME', '')
                        if any(keyword in item_name for keyword in ['利润', '溢利', '盈利', '收益', '净', '归属']):
                            profit_related.append((item_name, record.get('AMOUNT', 'N/A')))
                    
                    print(f"   利润相关字段 ({len(profit_related)} 个):")
                    for name, amount in profit_related[:10]:  # 只显示前10个
                        print(f"     {name}: {amount}")
                
            else:
                print(f"   请求失败: {response.status_code}")
                
        except Exception as e:
            print(f"   分析 {stock_code} 失败: {e}")
    
    # 统计所有字段名
    print(f"\n📈 字段统计 (共 {len(all_field_names)} 个字段):")
    field_counter = Counter(all_field_names)
    
    # 查找利润相关的字段
    profit_fields = []
    for field_name, count in field_counter.items():
        if any(keyword in field_name for keyword in ['利润', '溢利', '盈利', '收益', '净']):
            profit_fields.append((field_name, count))
    
    profit_fields.sort(key=lambda x: x[1], reverse=True)
    
    print("\n💰 利润相关字段 (按出现频次排序):")
    for field_name, count in profit_fields:
        print(f"   {field_name}: {count} 次")
    
    # 查找最可能的净利润字段
    print("\n🎯 最可能的净利润字段:")
    net_income_candidates = []
    for field_name, count in profit_fields:
        if any(keyword in field_name for keyword in ['净利润', '归属', '母公司', '股东应占']):
            net_income_candidates.append((field_name, count))
    
    if net_income_candidates:
        for field_name, count in net_income_candidates:
            print(f"   ✅ {field_name}: {count} 次")
    else:
        print("   ❌ 没有找到明确的净利润字段")
        print("   🔍 建议检查以下字段:")
        for field_name, count in profit_fields[:5]:
            print(f"     {field_name}: {count} 次")

def test_specific_profit_fields():
    """测试特定的利润字段"""
    print("\n🧪 测试特定利润字段...")
    
    stock_code = "00700"  # 腾讯
    formatted_code = f"{stock_code}.HK"
    
    url = f"https://datacenter.eastmoney.com/securities/api/data/v1/get?reportName=RPT_HKF10_FN_INCOME_PC&columns=ALL&quoteColumns=&filter=(SECUCODE%3D%22{formatted_code}%22)&pageNumber=1&pageSize=200&sortTypes=-1&sortColumns=REPORT_DATE&source=F10&client=PC"
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Referer': 'https://datacenter.eastmoney.com/',
        'Accept': 'application/json, text/javascript, */*; q=0.01'
    }
    
    try:
        response = requests.get(url, headers=headers, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            
            if 'result' in data and 'data' in data['result']:
                records = data['result']['data']
                
                # 按报告期分组
                date_groups = {}
                for record in records:
                    report_date = record.get('REPORT_DATE', '')[:10]
                    if report_date not in date_groups:
                        date_groups[report_date] = []
                    date_groups[report_date].append(record)
                
                # 分析最新的报告期
                latest_date = max(date_groups.keys())
                latest_records = date_groups[latest_date]
                
                print(f"   最新报告期: {latest_date}")
                print(f"   该期间的所有财务项目:")
                
                for i, record in enumerate(latest_records):
                    item_name = record.get('STD_ITEM_NAME', '')
                    amount = record.get('AMOUNT', 'N/A')
                    print(f"   {i+1:2d}. {item_name}: {amount}")
                
                # 查找可能的净利润项目
                print(f"\n   可能的净利润项目:")
                possible_net_income = []
                for record in latest_records:
                    item_name = record.get('STD_ITEM_NAME', '')
                    amount = record.get('AMOUNT', 'N/A')
                    
                    # 更广泛的搜索条件
                    if any(keyword in item_name for keyword in ['股东', '母公司', '归属', '应占', '净', '溢利']):
                        possible_net_income.append((item_name, amount))
                
                for name, amount in possible_net_income:
                    print(f"     ✅ {name}: {amount}")
                
    except Exception as e:
        print(f"   测试失败: {e}")

def compare_with_working_stock():
    """与有数据的股票进行对比"""
    print("\n🔄 与有数据的股票进行对比...")
    
    # 02378是唯一有净利润数据的股票
    working_stock = "02378"
    test_stock = "00700"
    
    for stock_code in [working_stock, test_stock]:
        print(f"\n📊 分析股票 {stock_code}...")
        formatted_code = f"{stock_code}.HK"
        
        url = f"https://datacenter.eastmoney.com/securities/api/data/v1/get?reportName=RPT_HKF10_FN_INCOME_PC&columns=ALL&quoteColumns=&filter=(SECUCODE%3D%22{formatted_code}%22)&pageNumber=1&pageSize=200&sortTypes=-1&sortColumns=REPORT_DATE&source=F10&client=PC"
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Referer': 'https://datacenter.eastmoney.com/',
            'Accept': 'application/json, text/javascript, */*; q=0.01'
        }
        
        try:
            response = requests.get(url, headers=headers, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                
                if 'result' in data and 'data' in data['result']:
                    records = data['result']['data']
                    
                    # 查找净利润相关字段
                    net_income_fields = []
                    for record in records:
                        item_name = record.get('STD_ITEM_NAME', '')
                        if any(keyword in item_name for keyword in ['净利润', '归属于母公司', '归母净利润']):
                            net_income_fields.append(item_name)
                    
                    unique_fields = list(set(net_income_fields))
                    print(f"   净利润字段: {unique_fields}")
                    
                    if not unique_fields:
                        # 查找其他可能的字段
                        other_fields = []
                        for record in records:
                            item_name = record.get('STD_ITEM_NAME', '')
                            if any(keyword in item_name for keyword in ['股东应占', '母公司股东', '归属', '溢利']):
                                other_fields.append(item_name)
                        
                        unique_other = list(set(other_fields))
                        print(f"   其他可能字段: {unique_other}")
        
        except Exception as e:
            print(f"   分析 {stock_code} 失败: {e}")

def main():
    """主函数"""
    print("🎯 分析东方财富网API字段结构")
    print("=" * 60)
    
    # 1. 分析API字段
    analyze_api_fields()
    
    # 2. 测试特定字段
    test_specific_profit_fields()
    
    # 3. 对比分析
    compare_with_working_stock()
    
    print(f"\n✅ 分析完成")

if __name__ == "__main__":
    main()
