# 腾讯控股(00700) 日内行情数据获取工具 - 增强版

## 🚀 功能特性

### ✅ 核心功能
- **多数据源支持**: Yahoo Finance, AKShare等多个数据源
- **实时数据获取**: 获取腾讯控股最新价格和行情信息
- **分时数据**: 获取详细的分钟级历史数据
- **技术指标计算**: MA, RSI, MACD, 布林带等常用技术指标
- **数据质量验证**: 自动检查数据完整性和异常值

### 📊 可视化功能
- **综合分析图表**: 价格走势、成交量、技术指标一览
- **多子图布局**: 清晰展示不同维度的数据
- **高质量输出**: 300 DPI高清图表保存

### 🔍 实时监控
- **智能警报系统**: 价格异动、成交量异常、技术指标信号
- **实时监控模式**: 可自定义监控间隔和持续时间
- **多级警报**: 高、中、低不同严重程度的警报

### 💾 数据管理
- **智能缓存**: 避免重复请求，提高效率
- **多格式输出**: CSV、图表、文本报告
- **详细日志**: 完整的操作记录和错误追踪

## 📦 安装依赖

```bash
pip install pandas numpy matplotlib seaborn akshare yfinance requests
```

## 🎯 使用方法

### 基本用法

```bash
# 获取今日分时数据（默认模式）
python tencent_intraday_data_fetcher.py

# 只获取实时数据
python tencent_intraday_data_fetcher.py realtime

# 获取历史数据（默认5天）
python tencent_intraday_data_fetcher.py historical

# 获取指定天数的历史数据
python tencent_intraday_data_fetcher.py historical 10
```

### 实时监控

```bash
# 启动实时监控（默认60秒间隔，持续60分钟）
python tencent_intraday_data_fetcher.py monitor

# 自定义监控参数（30秒间隔，持续120分钟）
python tencent_intraday_data_fetcher.py monitor 30 120
```

### 配置管理

```bash
# 创建配置文件
python tencent_intraday_data_fetcher.py config

# 查看帮助
python tencent_intraday_data_fetcher.py help
```

## ⚙️ 配置文件

创建 `tencent_fetcher_config.json` 文件来自定义设置：

```json
{
  "data_sources": {
    "preferred_order": ["yfinance", "akshare_hk_spot", "akshare_individual"],
    "timeout": 10,
    "retry_count": 3
  },
  "alerts": {
    "enabled": true,
    "price_threshold": 5.0,
    "volume_threshold": 2.0,
    "rsi_overbought": 70.0,
    "rsi_oversold": 30.0
  }
}
```

## 📁 输出文件

### 数据文件
- `results/tencent_intraday_YYYYMMDD.csv` - 分时数据CSV文件
- `cache/00700_intraday_YYYYMMDD.pkl` - 缓存的数据文件

### 图表文件
- `charts/tencent_comprehensive_YYYYMMDD.png` - 综合分析图表

### 报告文件
- `results/tencent_intraday_report_YYYYMMDD.txt` - 详细分析报告

### 日志文件
- `tencent_fetcher.log` - 运行日志

## 🚨 警报类型

### 价格警报
- **价格异动**: 价格变动超过设定阈值（默认5%）
- **突破警报**: 价格突破关键技术位

### 成交量警报
- **成交量异常**: 成交量超过平均值的倍数（默认2倍）

### 技术指标警报
- **RSI超买/超卖**: RSI指标超过70或低于30
- **MACD金叉/死叉**: MACD线与信号线交叉

## 📊 技术指标说明

### 移动平均线 (MA)
- MA5, MA10, MA20, MA50
- 用于判断价格趋势

### 相对强弱指数 (RSI)
- 14期RSI
- 判断超买超卖状态

### MACD指标
- 快线12期，慢线26期，信号线9期
- 判断趋势转换

### 布林带 (Bollinger Bands)
- 20期中轨，2倍标准差上下轨
- 判断价格波动区间

## 🔧 高级功能

### 数据验证
- 自动检查数据完整性
- 识别异常价格和成交量
- 时间序列一致性验证

### 多数据源容错
- 自动切换数据源
- 重试机制
- 数据质量评估

### 性能优化
- 智能缓存机制
- 异步数据获取
- 内存优化

## 📝 注意事项

1. **数据源限制**: 某些数据源可能有访问频率限制
2. **网络依赖**: 需要稳定的网络连接
3. **交易时间**: 港股交易时间为9:30-12:00, 13:00-16:00
4. **数据延迟**: 实时数据可能有几分钟延迟

## 🐛 故障排除

### 常见问题

1. **无法获取数据**
   - 检查网络连接
   - 确认数据源可用性
   - 查看日志文件

2. **图表生成失败**
   - 检查matplotlib后端设置
   - 确认字体文件存在
   - 查看错误日志

3. **缓存问题**
   - 清理cache目录
   - 检查磁盘空间
   - 重新运行程序

## 📞 技术支持

如有问题或建议，请查看日志文件 `tencent_fetcher.log` 获取详细错误信息。

## 🔄 版本历史

### v2.0 (增强版)
- 新增多数据源支持
- 新增实时监控功能
- 新增可视化图表
- 新增数据质量验证
- 新增配置文件支持
- 改进错误处理和日志记录

### v1.0 (基础版)
- 基本数据获取功能
- 简单技术指标计算
- CSV数据导出
