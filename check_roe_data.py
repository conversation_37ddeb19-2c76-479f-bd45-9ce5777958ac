#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ROE数据检查和分析脚本
"""

import sqlite3
import pandas as pd

def analyze_roe_data(db_name='ganggutong_financial_data.db'):
    """分析ROE数据质量"""
    
    conn = sqlite3.connect(db_name)
    
    # 获取ROE数据统计
    print("=== ROE数据质量分析 ===")
    
    # 基本统计
    query = """
    SELECT 
        COUNT(*) as total_records,
        COUNT(roe) as has_roe,
        ROUND((COUNT(roe) * 100.0 / COUNT(*)), 2) as roe_fill_rate,
        COUNT(CASE WHEN roe IS NOT NULL AND roe BETWEEN -1 AND 1 THEN 1 END) as reasonable_roe,
        COUNT(CASE WHEN roe IS NOT NULL AND (roe < -1 OR roe > 1) THEN 1 END) as abnormal_roe
    FROM financial_data
    """
    
    result = conn.execute(query).fetchone()
    print(f"总记录数: {result[0]}")
    print(f"有ROE数据: {result[1]} ({result[2]}%)")
    print(f"合理ROE (-100%~100%): {result[3]}")
    print(f"异常ROE: {result[4]}")
    
    # 异常ROE详情
    if result[4] > 0:
        print(f"\n=== 异常ROE数据 (前20条) ===")
        abnormal_query = """
        SELECT stock_code, report_date, 
               ROUND(roe*100, 2) as roe_percent,
               net_income, shareholders_equity
        FROM financial_data 
        WHERE roe IS NOT NULL AND (roe < -1 OR roe > 1)
        ORDER BY ABS(roe) DESC
        LIMIT 20
        """
        
        abnormal_data = conn.execute(abnormal_query).fetchall()
        for row in abnormal_data:
            print(f"股票: {row[0]}, 日期: {row[1]}, ROE: {row[2]}%, 净利润: {row[3]}, 股东权益: {row[4]}")
    
    # 最新合理ROE数据
    print(f"\n=== 最新合理ROE数据 (前15条) ===")
    recent_query = """
    SELECT stock_code, report_date, 
           ROUND(roe*100, 2) as roe_percent,
           revenue, net_income, shareholders_equity
    FROM financial_data 
    WHERE roe IS NOT NULL AND roe BETWEEN -1 AND 1 
          AND report_date >= '2024-01-01'
    ORDER BY report_date DESC, roe DESC
    LIMIT 15
    """
    
    recent_data = conn.execute(recent_query).fetchall()
    for row in recent_data:
        print(f"股票: {row[0]}, 日期: {row[1]}, ROE: {row[2]}%, 营收: {row[3]:.0f}, 净利润: {row[4]:.0f}")
    
    # 按股票统计ROE
    print(f"\n=== ROE最高的股票 (合理范围内) ===")
    top_roe_query = """
    SELECT stock_code, 
           COUNT(*) as record_count,
           ROUND(AVG(roe)*100, 2) as avg_roe_percent,
           ROUND(MAX(roe)*100, 2) as max_roe_percent,
           MAX(report_date) as latest_date
    FROM financial_data 
    WHERE roe IS NOT NULL AND roe BETWEEN 0 AND 1
    GROUP BY stock_code
    HAVING COUNT(*) >= 3
    ORDER BY AVG(roe) DESC
    LIMIT 10
    """
    
    top_roe_data = conn.execute(top_roe_query).fetchall()
    for row in top_roe_data:
        print(f"股票: {row[0]}, 记录数: {row[1]}, 平均ROE: {row[2]}%, 最高ROE: {row[3]}%, 最新日期: {row[4]}")
    
    conn.close()

def export_roe_data(db_name='ganggutong_financial_data.db', output_file='roe_analysis.csv'):
    """导出ROE数据用于进一步分析"""
    
    conn = sqlite3.connect(db_name)
    
    query = """
    SELECT stock_code, report_date, 
           roe, revenue, net_income, shareholders_equity,
           CASE 
               WHEN roe IS NULL THEN 'Missing'
               WHEN roe BETWEEN -1 AND 1 THEN 'Normal'
               ELSE 'Abnormal'
           END as roe_status
    FROM financial_data 
    WHERE report_date >= '2023-01-01'
    ORDER BY stock_code, report_date DESC
    """
    
    df = pd.read_sql_query(query, conn)
    df.to_csv(output_file, index=False, encoding='utf-8-sig')
    print(f"\nROE数据已导出到: {output_file}")
    
    conn.close()

if __name__ == "__main__":
    analyze_roe_data()
    export_roe_data()
