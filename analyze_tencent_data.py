#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专门分析腾讯(00700)的财务数据结构
"""

import requests
import json
import pandas as pd
from datetime import datetime

def analyze_tencent_financial_data():
    """详细分析腾讯的财务数据"""
    print("🔍 分析腾讯(00700)财务数据...")
    
    stock_code = "00700"
    formatted_code = f"{stock_code}.HK"
    
    url = f"https://datacenter.eastmoney.com/securities/api/data/v1/get?reportName=RPT_HKF10_FN_INCOME_PC&columns=ALL&quoteColumns=&filter=(SECUCODE%3D%22{formatted_code}%22)&pageNumber=1&pageSize=200&sortTypes=-1&sortColumns=REPORT_DATE&source=F10&client=PC"
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Referer': 'https://datacenter.eastmoney.com/',
        'Accept': 'application/json, text/javascript, */*; q=0.01'
    }
    
    try:
        response = requests.get(url, headers=headers, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            
            if 'result' in data and 'data' in data['result']:
                records = data['result']['data']
                print(f"✅ 成功获取 {len(records)} 条记录")
                
                # 按报告期分组
                date_groups = {}
                for record in records:
                    report_date = record.get('REPORT_DATE', '')[:10]
                    if report_date not in date_groups:
                        date_groups[report_date] = []
                    date_groups[report_date].append(record)
                
                print(f"📅 报告期数量: {len(date_groups)}")
                sorted_dates = sorted(date_groups.keys(), reverse=True)
                
                # 分析最近几个报告期
                for i, report_date in enumerate(sorted_dates[:4]):
                    print(f"\n📊 报告期 {report_date} (第{i+1}个):")
                    items = date_groups[report_date]
                    
                    # 构建财务记录
                    financial_record = {
                        'stock_code': stock_code,
                        'report_date': report_date,
                        'period_type': 'quarterly'
                    }
                    
                    print(f"   该期间共有 {len(items)} 个财务项目")
                    
                    # 查找各种财务指标
                    revenue_items = []
                    profit_items = []
                    other_items = []
                    
                    for item in items:
                        item_name = item.get('STD_ITEM_NAME', '')
                        amount = item.get('AMOUNT')
                        
                        # 营收相关
                        if any(keyword in item_name for keyword in ['营业额', '营运收入', '经营收入总额', '营业收入', '收入总额']):
                            revenue_items.append((item_name, amount))
                            if 'revenue' not in financial_record or '营业额' in item_name:
                                financial_record['revenue'] = float(amount) * 10000 if amount else None
                        
                        # 利润相关
                        elif any(keyword in item_name for keyword in ['利润', '溢利', '盈利', '收益']):
                            profit_items.append((item_name, amount))
                            
                            # 检查是否是净利润
                            if any(keyword in item_name for keyword in ['股东应占溢利', '除税后溢利', '持续经营业务税后利润']):
                                if 'net_income' not in financial_record:
                                    financial_record['net_income'] = float(amount) * 10000 if amount else None
                        
                        else:
                            other_items.append((item_name, amount))
                    
                    # 显示营收项目
                    print(f"   💰 营收项目 ({len(revenue_items)} 个):")
                    for name, amount in revenue_items:
                        print(f"     {name}: {amount:,} 万元" if amount else f"     {name}: N/A")
                    
                    # 显示利润项目
                    print(f"   📈 利润项目 ({len(profit_items)} 个):")
                    for name, amount in profit_items:
                        is_net_income = any(keyword in name for keyword in ['股东应占溢利', '除税后溢利', '持续经营业务税后利润'])
                        marker = " ⭐" if is_net_income else ""
                        print(f"     {name}: {amount:,} 万元{marker}" if amount else f"     {name}: N/A{marker}")
                    
                    # 显示提取的财务数据
                    print(f"   📋 提取的财务数据:")
                    print(f"     营收: {financial_record.get('revenue', 'N/A'):,} 元" if financial_record.get('revenue') else "     营收: N/A")
                    print(f"     净利润: {financial_record.get('net_income', 'N/A'):,} 元" if financial_record.get('net_income') else "     净利润: N/A")
                    
                    # 计算利润率
                    if financial_record.get('revenue') and financial_record.get('net_income'):
                        net_margin = financial_record['net_income'] / financial_record['revenue']
                        print(f"     净利润率: {net_margin:.2%}")
                    
                    print("-" * 50)
                
                # 总结分析
                print(f"\n📋 总结分析:")
                
                # 统计所有利润相关字段
                all_profit_fields = {}
                for record in records:
                    item_name = record.get('STD_ITEM_NAME', '')
                    if any(keyword in item_name for keyword in ['利润', '溢利', '盈利', '收益']):
                        if item_name not in all_profit_fields:
                            all_profit_fields[item_name] = 0
                        all_profit_fields[item_name] += 1
                
                print(f"   所有利润相关字段 (按出现频次排序):")
                sorted_fields = sorted(all_profit_fields.items(), key=lambda x: x[1], reverse=True)
                for field_name, count in sorted_fields:
                    is_net_income = any(keyword in field_name for keyword in ['股东应占溢利', '除税后溢利', '持续经营业务税后利润'])
                    marker = " ⭐ (净利润候选)" if is_net_income else ""
                    print(f"     {field_name}: {count} 次{marker}")
                
                # 推荐的净利润字段
                print(f"\n💡 推荐的净利润字段:")
                recommended_fields = ['股东应占溢利', '除税后溢利', '持续经营业务税后利润']
                for field in recommended_fields:
                    if field in all_profit_fields:
                        print(f"   ✅ {field}: 出现 {all_profit_fields[field]} 次")
                    else:
                        print(f"   ❌ {field}: 未找到")
                
            else:
                print("❌ API响应格式异常")
        else:
            print(f"❌ API请求失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        import traceback
        traceback.print_exc()

def test_net_income_extraction():
    """测试净利润提取逻辑"""
    print(f"\n🧪 测试净利润提取逻辑...")
    
    # 模拟当前的提取逻辑
    current_keywords = ['净利润', '归属于母公司', '归母净利润']
    
    # 建议的新关键词
    new_keywords = ['净利润', '归属于母公司', '归母净利润', '股东应占溢利', '除税后溢利', '持续经营业务税后利润']
    
    # 腾讯实际的利润字段
    tencent_fields = [
        '股东应占溢利',
        '除税后溢利', 
        '除税前溢利',
        '经营溢利',
        '持续经营业务税后利润',
        '每股基本盈利',
        '每股摊薄盈利'
    ]
    
    print(f"   当前关键词: {current_keywords}")
    print(f"   建议关键词: {new_keywords}")
    print(f"   腾讯实际字段: {tencent_fields}")
    
    print(f"\n   匹配测试:")
    for field in tencent_fields:
        current_match = any(keyword in field for keyword in current_keywords)
        new_match = any(keyword in field for keyword in new_keywords)
        
        print(f"     {field}:")
        print(f"       当前逻辑: {'✅ 匹配' if current_match else '❌ 不匹配'}")
        print(f"       新逻辑: {'✅ 匹配' if new_match else '❌ 不匹配'}")

def compare_with_database():
    """与数据库中的数据进行对比"""
    print(f"\n🔄 与数据库数据对比...")
    
    import sqlite3
    
    try:
        conn = sqlite3.connect('ganggutong_financial_data.db')
        
        # 查询腾讯的数据
        query = """
        SELECT report_date, revenue, net_income, gross_profit, operating_income
        FROM financial_data 
        WHERE stock_code = '00700'
        ORDER BY report_date DESC
        LIMIT 5
        """
        
        df = pd.read_sql_query(query, conn)
        conn.close()
        
        if not df.empty:
            print(f"   数据库中腾讯的数据:")
            print(df.to_string(index=False))
        else:
            print(f"   数据库中没有腾讯的数据")
            
    except Exception as e:
        print(f"   数据库查询失败: {e}")

def main():
    """主函数"""
    print("🎯 腾讯(00700)财务数据详细分析")
    print("=" * 60)
    
    # 1. 分析财务数据
    analyze_tencent_financial_data()
    
    # 2. 测试提取逻辑
    test_net_income_extraction()
    
    # 3. 与数据库对比
    compare_with_database()
    
    print(f"\n✅ 分析完成 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
