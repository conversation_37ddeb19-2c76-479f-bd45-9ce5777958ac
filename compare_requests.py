#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
对比成功和失败的请求
"""

import requests
import json
import time
import random

def test_successful_request():
    """测试成功的请求（直接调用）"""
    print("🔍 测试成功的请求...")
    
    stock_code = "00700"
    formatted_code = f"{stock_code}.HK"
    
    url = f"https://datacenter.eastmoney.com/securities/api/data/v1/get?reportName=RPT_HKF10_FN_INCOME_PC&columns=ALL&quoteColumns=&filter=(SECUCODE%3D%22{formatted_code}%22)&pageNumber=1&pageSize=200&sortTypes=-1&sortColumns=REPORT_DATE&source=F10&client=PC"
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Referer': 'https://datacenter.eastmoney.com/',
        'Accept': 'application/json, text/javascript, */*; q=0.01'
    }
    
    response = requests.get(url, headers=headers, timeout=30)
    data = response.json()
    
    print(f"   状态码: {response.status_code}")
    print(f"   success: {data.get('success')}")
    print(f"   message: {data.get('message')}")
    print(f"   result is None: {data.get('result') is None}")
    
    if data.get('result') and data['result'].get('data'):
        print(f"   数据条数: {len(data['result']['data'])}")
        return True
    else:
        print(f"   无数据")
        return False

def test_downloader_style_request():
    """测试下载器风格的请求"""
    print(f"\n🔍 测试下载器风格的请求...")
    
    stock_code = "00700"
    formatted_code = f"{stock_code}.HK"
    
    url = f"https://datacenter.eastmoney.com/securities/api/data/v1/get?reportName=RPT_HKF10_FN_INCOME_PC&columns=ALL&quoteColumns=&filter=(SECUCODE%3D%22{formatted_code}%22)&pageNumber=1&pageSize=200&sortTypes=-1&sortColumns=REPORT_DATE&source=F10&client=PC"
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Referer': 'https://datacenter.eastmoney.com/',
        'Accept': 'application/json, text/javascript, */*; q=0.01',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache'
    }
    
    # 添加随机延迟
    time.sleep(random.uniform(1, 3))
    
    # 使用会话
    session = requests.Session()
    session.headers.update(headers)
    response = session.get(url, timeout=30)
    data = response.json()
    
    print(f"   状态码: {response.status_code}")
    print(f"   success: {data.get('success')}")
    print(f"   message: {data.get('message')}")
    print(f"   result is None: {data.get('result') is None}")
    
    if data.get('result') and data['result'].get('data'):
        print(f"   数据条数: {len(data['result']['data'])}")
        return True
    else:
        print(f"   无数据")
        return False

def test_different_parameters():
    """测试不同的参数组合"""
    print(f"\n🔍 测试不同的参数组合...")
    
    stock_code = "00700"
    formatted_code = f"{stock_code}.HK"
    
    # 测试不同的参数组合
    test_cases = [
        {
            "name": "原始参数",
            "params": {
                "reportName": "RPT_HKF10_FN_INCOME_PC",
                "columns": "ALL",
                "quoteColumns": "",
                "filter": f"(SECUCODE=\"{formatted_code}\")",
                "pageNumber": "1",
                "pageSize": "200",
                "sortTypes": "-1",
                "sortColumns": "REPORT_DATE",
                "source": "F10",
                "client": "PC"
            }
        },
        {
            "name": "简化参数",
            "params": {
                "reportName": "RPT_HKF10_FN_INCOME_PC",
                "filter": f"(SECUCODE=\"{formatted_code}\")",
                "pageNumber": "1",
                "pageSize": "50"
            }
        },
        {
            "name": "不同pageSize",
            "params": {
                "reportName": "RPT_HKF10_FN_INCOME_PC",
                "columns": "ALL",
                "filter": f"(SECUCODE=\"{formatted_code}\")",
                "pageNumber": "1",
                "pageSize": "50",
                "sortTypes": "-1",
                "sortColumns": "REPORT_DATE"
            }
        }
    ]
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Referer': 'https://datacenter.eastmoney.com/',
        'Accept': 'application/json, text/javascript, */*; q=0.01'
    }
    
    for test_case in test_cases:
        print(f"\n   测试: {test_case['name']}")
        
        # 构建URL
        base_url = "https://datacenter.eastmoney.com/securities/api/data/v1/get"
        params = test_case['params']
        
        try:
            response = requests.get(base_url, params=params, headers=headers, timeout=30)
            data = response.json()
            
            print(f"     状态码: {response.status_code}")
            print(f"     success: {data.get('success')}")
            print(f"     message: {data.get('message')}")
            
            if data.get('result') and data['result'].get('data'):
                print(f"     ✅ 数据条数: {len(data['result']['data'])}")
            else:
                print(f"     ❌ 无数据")
        
        except Exception as e:
            print(f"     ❌ 请求失败: {e}")
        
        # 间隔
        time.sleep(2)

def test_different_stocks():
    """测试不同的股票"""
    print(f"\n🔍 测试不同的股票...")
    
    test_stocks = ["00700", "00941", "01299", "02318", "03690"]
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Referer': 'https://datacenter.eastmoney.com/',
        'Accept': 'application/json, text/javascript, */*; q=0.01'
    }
    
    for stock_code in test_stocks:
        print(f"\n   测试股票: {stock_code}")
        formatted_code = f"{stock_code}.HK"
        
        url = f"https://datacenter.eastmoney.com/securities/api/data/v1/get?reportName=RPT_HKF10_FN_INCOME_PC&columns=ALL&quoteColumns=&filter=(SECUCODE%3D%22{formatted_code}%22)&pageNumber=1&pageSize=200&sortTypes=-1&sortColumns=REPORT_DATE&source=F10&client=PC"
        
        try:
            response = requests.get(url, headers=headers, timeout=30)
            data = response.json()
            
            print(f"     success: {data.get('success')}")
            print(f"     message: {data.get('message')}")
            
            if data.get('result') and data['result'].get('data'):
                print(f"     ✅ 数据条数: {len(data['result']['data'])}")
            else:
                print(f"     ❌ 无数据")
        
        except Exception as e:
            print(f"     ❌ 请求失败: {e}")
        
        time.sleep(1)

def main():
    """主函数"""
    print("🎯 对比成功和失败的请求")
    print("=" * 60)
    
    # 1. 测试成功的请求
    success1 = test_successful_request()
    
    # 2. 测试下载器风格的请求
    success2 = test_downloader_style_request()
    
    # 3. 测试不同参数
    test_different_parameters()
    
    # 4. 测试不同股票
    test_different_stocks()
    
    print(f"\n📊 总结:")
    print(f"   直接请求成功: {success1}")
    print(f"   下载器风格请求成功: {success2}")
    
    print(f"\n✅ 测试完成")

if __name__ == "__main__":
    main()
