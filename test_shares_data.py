#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试股本数据获取
"""

import requests
import json
import time
import pandas as pd

def test_shares_data_api(stock_code='00700'):
    """测试股本数据API"""
    try:
        formatted_code = f'{stock_code}.HK'
        
        # 尝试不同的API接口获取股本数据
        apis = [
            # 主要经营数据接口
            f"https://datacenter.eastmoney.com/securities/api/data/v1/get?reportName=RPT_HKF10_FN_MAINOP_PC&columns=ALL&quoteColumns=&filter=(SECUCODE%3D%22{formatted_code}%22)&pageNumber=1&pageSize=20&sortTypes=-1&sortColumns=REPORT_DATE&source=F10&client=PC",
            
            # 财务指标接口
            f"https://datacenter.eastmoney.com/securities/api/data/v1/get?reportName=RPT_HKF10_FN_INDICATOR_PC&columns=ALL&quoteColumns=&filter=(SECUCODE%3D%22{formatted_code}%22)&pageNumber=1&pageSize=20&sortTypes=-1&sortColumns=REPORT_DATE&source=F10&client=PC",
            
            # 股本结构接口
            f"https://datacenter.eastmoney.com/securities/api/data/v1/get?reportName=RPT_HKF10_FN_EQUITY_PC&columns=ALL&quoteColumns=&filter=(SECUCODE%3D%22{formatted_code}%22)&pageNumber=1&pageSize=20&sortTypes=-1&sortColumns=REPORT_DATE&source=F10&client=PC",
        ]
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Referer': 'https://datacenter.eastmoney.com/',
        }
        
        for i, url in enumerate(apis, 1):
            print(f"\n=== 测试API {i} ===")
            try:
                response = requests.get(url, headers=headers, timeout=30)
                if response.status_code == 200:
                    data = response.json()
                    if data.get('success') and data.get('result') and data['result'].get('data'):
                        items = data['result']['data']
                        print(f"获取到 {len(items)} 条记录")
                        
                        # 显示前几条记录的字段
                        if items:
                            print("字段列表:")
                            for key in items[0].keys():
                                print(f"  {key}")
                            
                            print("\n前3条记录:")
                            for j, item in enumerate(items[:3]):
                                print(f"记录 {j+1}:")
                                for key, value in item.items():
                                    if value is not None and str(value).strip():
                                        print(f"  {key}: {value}")
                                print("-" * 30)
                    else:
                        print("API返回无数据")
                else:
                    print(f"请求失败: {response.status_code}")
            except Exception as e:
                print(f"API {i} 请求失败: {e}")
            
            time.sleep(2)  # 避免请求过快
            
    except Exception as e:
        print(f"测试失败: {e}")

def test_specific_shares_fields():
    """测试特定的股本相关字段"""
    stock_code = '00700'
    formatted_code = f'{stock_code}.HK'
    
    # 尝试财务指标接口，通常包含股本数据
    url = f"https://datacenter.eastmoney.com/securities/api/data/v1/get?reportName=RPT_HKF10_FN_INDICATOR_PC&columns=ALL&quoteColumns=&filter=(SECUCODE%3D%22{formatted_code}%22)&pageNumber=1&pageSize=10&sortTypes=-1&sortColumns=REPORT_DATE&source=F10&client=PC"
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Referer': 'https://datacenter.eastmoney.com/',
    }
    
    try:
        response = requests.get(url, headers=headers, timeout=30)
        if response.status_code == 200:
            data = response.json()
            if data.get('success') and data.get('result') and data['result'].get('data'):
                items = data['result']['data']
                print(f"=== 腾讯财务指标数据 ===")
                print(f"获取到 {len(items)} 条记录")
                
                # 查找股本相关字段
                shares_keywords = ['股本', '股份', 'SHARES', 'SHARE', '流通', '总股']
                
                for item in items[:5]:  # 只看前5条
                    report_date = item.get('REPORT_DATE', '')[:10]
                    print(f"\n报告期: {report_date}")
                    
                    for key, value in item.items():
                        if value is not None and any(keyword in str(key).upper() for keyword in shares_keywords):
                            print(f"  {key}: {value}")
            else:
                print("无财务指标数据")
        else:
            print(f"请求失败: {response.status_code}")
            
    except Exception as e:
        print(f"测试失败: {e}")

if __name__ == "__main__":
    print("开始测试股本数据获取...")
    test_shares_data_api()
    print("\n" + "="*50)
    test_specific_shares_fields()
