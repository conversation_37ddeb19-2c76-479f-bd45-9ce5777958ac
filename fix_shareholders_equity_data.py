#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复股东权益数据的脚本
由于之前的代码错误地将东方财富API返回的数据乘以了10000，导致数据被放大了10000倍
这个脚本将修正数据库中的错误数据
"""

import sqlite3
import pandas as pd
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('fix_shareholders_equity.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class ShareholdersEquityFixer:
    """股东权益数据修复器"""
    
    def __init__(self, db_name='ganggutong_financial_data.db'):
        self.db_name = db_name
        
    def analyze_data_issues(self):
        """分析数据问题"""
        try:
            conn = sqlite3.connect(self.db_name)
            
            # 查询所有财务数据，检查异常值
            query = """
            SELECT stock_code, report_date, 
                   shareholders_equity, total_assets, total_debt,
                   revenue, net_income, roe
            FROM financial_data 
            WHERE shareholders_equity IS NOT NULL
            ORDER BY shareholders_equity DESC
            LIMIT 20
            """
            
            df = pd.read_sql_query(query, conn)
            conn.close()
            
            logger.info("=== 股东权益数据异常分析 ===")
            logger.info(f"检查前20个最大的股东权益值:")
            
            for _, row in df.iterrows():
                equity = row['shareholders_equity']
                equity_trillion = equity / 1e12
                logger.info(f"股票: {row['stock_code']}, 日期: {row['report_date']}, "
                          f"股东权益: {equity:,.0f} 元 ({equity_trillion:.2f} 万亿元)")
                
                # 如果股东权益超过100万亿，很可能是错误数据
                if equity > 100e12:
                    logger.warning(f"  ⚠️ 异常数据: 股东权益过大 ({equity_trillion:.2f} 万亿元)")
            
            return df
            
        except Exception as e:
            logger.error(f"分析数据问题失败: {e}")
            return pd.DataFrame()
    
    def identify_affected_records(self):
        """识别受影响的记录"""
        try:
            conn = sqlite3.connect(self.db_name)
            
            # 查找可能被错误放大的记录
            # 假设正常的股东权益不会超过50万亿元（腾讯约1万亿，阿里约3万亿）
            query = """
            SELECT id, stock_code, report_date,
                   shareholders_equity, total_assets, total_debt,
                   revenue, net_income, roe
            FROM financial_data 
            WHERE shareholders_equity > 50000000000000  -- 50万亿元
            ORDER BY stock_code, report_date
            """
            
            df = pd.read_sql_query(query, conn)
            conn.close()
            
            logger.info(f"发现 {len(df)} 条可能被错误放大的记录")
            
            # 按股票分组显示
            if not df.empty:
                for stock_code in df['stock_code'].unique():
                    stock_data = df[df['stock_code'] == stock_code]
                    logger.info(f"股票 {stock_code}: {len(stock_data)} 条异常记录")
            
            return df
            
        except Exception as e:
            logger.error(f"识别受影响记录失败: {e}")
            return pd.DataFrame()
    
    def fix_financial_data(self, dry_run=True):
        """修复财务数据"""
        try:
            affected_records = self.identify_affected_records()
            
            if affected_records.empty:
                logger.info("没有发现需要修复的数据")
                return
            
            conn = sqlite3.connect(self.db_name)
            cursor = conn.cursor()
            
            fixed_count = 0
            
            for _, record in affected_records.iterrows():
                record_id = record['id']
                stock_code = record['stock_code']
                report_date = record['report_date']
                
                # 计算修正后的值（除以10000）
                old_equity = record['shareholders_equity']
                old_assets = record['total_assets']
                old_debt = record['total_debt']
                old_revenue = record['revenue']
                old_net_income = record['net_income']
                
                new_equity = old_equity / 10000 if old_equity else None
                new_assets = old_assets / 10000 if old_assets else None
                new_debt = old_debt / 10000 if old_debt else None
                new_revenue = old_revenue / 10000 if old_revenue else None
                new_net_income = old_net_income / 10000 if old_net_income else None
                
                # 重新计算ROE
                new_roe = None
                if new_equity and new_net_income and new_equity != 0:
                    roe_value = new_net_income / new_equity
                    if -1 <= roe_value <= 1:  # 合理范围
                        new_roe = roe_value
                
                # 重新计算其他比率
                new_roa = None
                if new_assets and new_net_income and new_assets != 0:
                    new_roa = new_net_income / new_assets
                
                new_debt_to_equity = None
                if new_assets and new_debt and new_assets != 0:
                    new_debt_to_equity = new_debt / new_assets
                
                logger.info(f"修复 {stock_code} {report_date}:")
                logger.info(f"  股东权益: {old_equity:,.0f} -> {new_equity:,.0f} 元")
                logger.info(f"  总资产: {old_assets:,.0f} -> {new_assets:,.0f} 元")
                logger.info(f"  ROE: {record['roe']} -> {new_roe}")
                
                if not dry_run:
                    # 更新数据库
                    cursor.execute("""
                        UPDATE financial_data 
                        SET shareholders_equity = ?,
                            total_assets = ?,
                            total_debt = ?,
                            revenue = ?,
                            net_income = ?,
                            roe = ?,
                            roa = ?,
                            debt_to_equity = ?
                        WHERE id = ?
                    """, (
                        new_equity, new_assets, new_debt, new_revenue, new_net_income,
                        new_roe, new_roa, new_debt_to_equity, record_id
                    ))
                    
                    fixed_count += 1
            
            if not dry_run:
                conn.commit()
                logger.info(f"✅ 成功修复 {fixed_count} 条记录")
            else:
                logger.info(f"🔍 预览模式: 将修复 {len(affected_records)} 条记录")
                logger.info("使用 dry_run=False 参数执行实际修复")
            
            conn.close()
            
        except Exception as e:
            logger.error(f"修复财务数据失败: {e}")
            if 'conn' in locals():
                conn.rollback()
                conn.close()
    
    def verify_fix(self):
        """验证修复结果"""
        try:
            conn = sqlite3.connect(self.db_name)
            
            # 检查腾讯的数据
            query = """
            SELECT report_date, 
                   shareholders_equity/1e12 as equity_trillion,
                   total_assets/1e12 as assets_trillion,
                   revenue/1e12 as revenue_trillion,
                   net_income/1e12 as income_trillion,
                   roe*100 as roe_percent
            FROM financial_data 
            WHERE stock_code = '00700' 
            ORDER BY report_date DESC
            LIMIT 5
            """
            
            df = pd.read_sql_query(query, conn)
            conn.close()
            
            logger.info("=== 修复后腾讯数据验证 ===")
            for _, row in df.iterrows():
                logger.info(f"报告期: {row['report_date']}")
                logger.info(f"  股东权益: {row['equity_trillion']:.2f} 万亿元")
                logger.info(f"  总资产: {row['assets_trillion']:.2f} 万亿元")
                logger.info(f"  营收: {row['revenue_trillion']:.2f} 万亿元")
                logger.info(f"  净利润: {row['income_trillion']:.2f} 万亿元")
                logger.info(f"  ROE: {row['roe_percent']:.2f}%" if pd.notna(row['roe_percent']) else "  ROE: None")
                logger.info("-" * 40)
            
        except Exception as e:
            logger.error(f"验证修复结果失败: {e}")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='修复股东权益数据')
    parser.add_argument('--db-name', default='ganggutong_financial_data.db',
                       help='数据库文件名')
    parser.add_argument('--analyze', action='store_true',
                       help='只分析数据问题，不进行修复')
    parser.add_argument('--fix', action='store_true',
                       help='执行实际修复（默认为预览模式）')
    parser.add_argument('--verify', action='store_true',
                       help='验证修复结果')
    
    args = parser.parse_args()
    
    fixer = ShareholdersEquityFixer(args.db_name)
    
    if args.analyze:
        fixer.analyze_data_issues()
    elif args.verify:
        fixer.verify_fix()
    else:
        # 默认先分析，再修复
        fixer.analyze_data_issues()
        fixer.identify_affected_records()
        fixer.fix_financial_data(dry_run=not args.fix)
        
        if args.fix:
            fixer.verify_fix()

if __name__ == "__main__":
    main()
