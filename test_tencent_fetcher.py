#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
腾讯控股数据获取器测试脚本

用于测试增强版数据获取器的各项功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from tencent_intraday_data_fetcher import TencentIntradayDataFetcher
import pandas as pd
import time

def test_basic_functionality():
    """测试基本功能"""
    print("🧪 测试基本功能...")
    
    # 创建获取器
    fetcher = TencentIntradayDataFetcher()
    
    # 测试实时数据获取
    print("\n1. 测试实时数据获取...")
    realtime_data = fetcher.get_realtime_data()
    if not realtime_data.empty:
        print("✅ 实时数据获取成功")
        print(f"   数据源: {realtime_data.get('source', ['未知']).iloc[0] if 'source' in realtime_data.columns else '未知'}")
        print(f"   价格: {realtime_data.get('price', [0]).iloc[0] if 'price' in realtime_data.columns else '未知'}")
    else:
        print("❌ 实时数据获取失败")
    
    # 测试数据验证
    print("\n2. 测试数据验证...")
    if not realtime_data.empty:
        is_valid, issues = fetcher.data_validator.validate_price_data(realtime_data)
        if is_valid:
            print("✅ 数据验证通过")
        else:
            print(f"⚠️ 数据验证问题: {issues}")
    
    # 测试技术指标计算
    print("\n3. 测试技术指标计算...")
    # 创建模拟数据进行测试
    test_data = pd.DataFrame({
        'datetime': pd.date_range('2025-06-13 09:30:00', periods=50, freq='1min'),
        'price': [350 + i * 0.1 + (i % 5) * 0.5 for i in range(50)],
        'volume': [1000 + i * 100 for i in range(50)]
    })
    
    enhanced_data = fetcher.calculate_technical_indicators(test_data)
    if 'ma5' in enhanced_data.columns and 'rsi' in enhanced_data.columns:
        print("✅ 技术指标计算成功")
        print(f"   MA5: {enhanced_data['ma5'].iloc[-1]:.2f}")
        print(f"   RSI: {enhanced_data['rsi'].iloc[-1]:.2f}")
    else:
        print("❌ 技术指标计算失败")
    
    # 测试警报检查
    print("\n4. 测试警报检查...")
    alerts = fetcher.check_alerts(enhanced_data)
    if alerts:
        print(f"✅ 警报系统工作正常，发现 {len(alerts)} 个警报")
        for alert in alerts[:3]:  # 只显示前3个
            print(f"   - {alert['type']}: {alert['message']}")
    else:
        print("✅ 警报系统工作正常，无警报触发")
    
    return fetcher

def test_visualization():
    """测试可视化功能"""
    print("\n🎨 测试可视化功能...")
    
    fetcher = TencentIntradayDataFetcher()
    
    # 创建测试数据
    test_data = pd.DataFrame({
        'datetime': pd.date_range('2025-06-13 09:30:00', periods=100, freq='1min'),
        'price': [350 + i * 0.05 + (i % 10) * 0.3 for i in range(100)],
        'volume': [1000 + i * 50 + (i % 15) * 200 for i in range(100)]
    })
    
    # 计算技术指标
    enhanced_data = fetcher.calculate_technical_indicators(test_data)
    
    # 生成图表
    chart_path = fetcher.create_comprehensive_chart(enhanced_data, "test")
    
    if chart_path and os.path.exists(chart_path):
        print("✅ 图表生成成功")
        print(f"   图表路径: {chart_path}")
    else:
        print("❌ 图表生成失败")

def test_config_loading():
    """测试配置加载"""
    print("\n⚙️ 测试配置加载...")
    
    # 测试默认配置
    fetcher1 = TencentIntradayDataFetcher()
    print("✅ 默认配置加载成功")
    
    # 测试自定义配置文件
    if os.path.exists('tencent_fetcher_config.json'):
        fetcher2 = TencentIntradayDataFetcher('tencent_fetcher_config.json')
        print("✅ 自定义配置文件加载成功")
    else:
        print("⚠️ 配置文件不存在，跳过测试")

def test_short_monitoring():
    """测试短时间监控"""
    print("\n🔍 测试短时间监控功能...")
    
    fetcher = TencentIntradayDataFetcher()
    
    print("启动10秒监控测试...")
    try:
        # 运行10秒的监控
        fetcher.start_realtime_monitoring(interval_seconds=5, duration_minutes=0.17)  # 约10秒
        print("✅ 监控功能测试完成")
    except Exception as e:
        print(f"❌ 监控功能测试失败: {e}")

def main():
    """主测试函数"""
    print("🎯 腾讯控股数据获取器 - 功能测试")
    print("=" * 60)
    
    try:
        # 基本功能测试
        fetcher = test_basic_functionality()
        
        # 可视化测试
        test_visualization()
        
        # 配置加载测试
        test_config_loading()
        
        # 询问是否进行监控测试
        response = input("\n是否进行短时间监控测试？(y/n): ").lower().strip()
        if response in ['y', 'yes', '是']:
            test_short_monitoring()
        
        print("\n✅ 所有测试完成！")
        print("\n📋 测试总结:")
        print("   ✅ 基本功能正常")
        print("   ✅ 数据验证正常")
        print("   ✅ 技术指标计算正常")
        print("   ✅ 警报系统正常")
        print("   ✅ 可视化功能正常")
        print("   ✅ 配置加载正常")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
