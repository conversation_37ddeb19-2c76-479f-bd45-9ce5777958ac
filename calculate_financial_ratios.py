#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
财务指标计算脚本
基于原始财务数据计算各种财务比率和指标
"""

import sqlite3
import pandas as pd
import logging
from datetime import datetime
import argparse

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('financial_ratios_calculation.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class FinancialRatiosCalculator:
    """财务指标计算器"""
    
    def __init__(self, db_name='ganggutong_financial_data.db'):
        self.db_name = db_name
        self.setup_ratios_table()
    
    def setup_ratios_table(self):
        """创建财务比率表"""
        try:
            conn = sqlite3.connect(self.db_name)
            cursor = conn.cursor()
            
            # 创建财务比率表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS financial_ratios (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    stock_code TEXT NOT NULL,
                    report_date TEXT NOT NULL,
                    period_type TEXT NOT NULL,
                    -- 盈利能力指标
                    roe REAL,                       -- 净资产收益率 (单季度)
                    roe_ttm REAL,                   -- 净资产收益率 (TTM)
                    roa REAL,                       -- 总资产收益率
                    roic REAL,                      -- 投入资本回报率
                    gross_margin REAL,              -- 毛利率
                    operating_margin REAL,          -- 营业利润率
                    net_margin REAL,                -- 净利润率
                    -- 偿债能力指标
                    debt_to_equity REAL,            -- 资产负债率
                    debt_to_assets REAL,            -- 负债权益比
                    current_ratio REAL,             -- 流动比率
                    quick_ratio REAL,               -- 速动比率
                    -- 营运能力指标
                    asset_turnover REAL,            -- 总资产周转率
                    equity_turnover REAL,           -- 净资产周转率
                    -- 成长能力指标
                    revenue_growth_yoy REAL,        -- 营收同比增长率
                    net_income_growth_yoy REAL,     -- 净利润同比增长率
                    equity_growth_yoy REAL,         -- 净资产同比增长率
                    -- 每股指标
                    eps REAL,                       -- 每股收益
                    book_value_per_share REAL,      -- 每股净资产
                    revenue_per_share REAL,         -- 每股营收
                    -- 计算时间
                    calculated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(stock_code, report_date, period_type),
                    FOREIGN KEY (stock_code) REFERENCES stock_info (stock_code)
                )
            ''')
            
            # 创建索引
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_ratios_code_date ON financial_ratios(stock_code, report_date)')
            
            conn.commit()
            conn.close()
            logger.info("财务比率表初始化完成")
            
        except Exception as e:
            logger.error(f"创建财务比率表失败: {e}")
            raise
    
    def calculate_profitability_ratios(self, record):
        """计算盈利能力指标"""
        ratios = {}
        
        # 基础数据
        revenue = record.get('revenue')
        cost_of_revenue = record.get('cost_of_revenue')
        gross_profit = record.get('gross_profit')
        operating_income = record.get('operating_income')
        net_income = record.get('net_income')
        total_assets = record.get('total_assets')
        shareholders_equity = record.get('shareholders_equity')
        
        # 毛利率
        if revenue and revenue != 0:
            if gross_profit:
                ratios['gross_margin'] = gross_profit / revenue
            elif cost_of_revenue:
                ratios['gross_margin'] = (revenue - cost_of_revenue) / revenue
        
        # 营业利润率
        if revenue and operating_income and revenue != 0:
            ratios['operating_margin'] = operating_income / revenue
        
        # 净利润率
        if revenue and net_income and revenue != 0:
            ratios['net_margin'] = net_income / revenue
        
        # ROA (总资产收益率)
        if total_assets and net_income and total_assets != 0:
            ratios['roa'] = net_income / total_assets
        
        # ROE (净资产收益率) - 单季度
        if shareholders_equity and net_income and shareholders_equity != 0:
            roe_value = net_income / shareholders_equity
            # ROE合理性检查
            if -1 <= roe_value <= 1:
                ratios['roe'] = roe_value
        
        return ratios
    
    def calculate_roe_ttm(self, stock_code, current_date, current_equity):
        """计算ROE-TTM（最近4个季度净利润总和 / 股东权益）"""
        try:
            if not current_equity or current_equity == 0:
                return None
            
            conn = sqlite3.connect(self.db_name)
            
            # 获取该股票最近4个季度的净利润（包括当前季度）
            query = """
            SELECT net_income
            FROM financial_data 
            WHERE stock_code = ? AND report_date <= ? 
            AND net_income IS NOT NULL AND period_type = 'quarterly'
            ORDER BY report_date DESC
            LIMIT 4
            """
            
            cursor = conn.cursor()
            results = cursor.execute(query, (stock_code, current_date)).fetchall()
            conn.close()
            
            if len(results) == 4:
                # 计算最近4个季度净利润总和
                ttm_net_income = sum(row[0] for row in results)
                
                # 计算ROE-TTM
                roe_ttm = ttm_net_income / current_equity
                
                # ROE-TTM合理性检查
                if -2 <= roe_ttm <= 2:  # ROE-TTM范围可以比单季度ROE更宽
                    return roe_ttm
                else:
                    logger.warning(f"{stock_code} {current_date}: ROE-TTM异常 {roe_ttm*100:.2f}%")
                    return None
            else:
                # 数据不足4个季度
                return None
                
        except Exception as e:
            logger.warning(f"计算{stock_code} ROE-TTM失败: {e}")
            return None
    
    def calculate_solvency_ratios(self, record):
        """计算偿债能力指标"""
        ratios = {}
        
        total_assets = record.get('total_assets')
        total_debt = record.get('total_debt')
        current_assets = record.get('current_assets')
        current_liabilities = record.get('current_liabilities')
        shareholders_equity = record.get('shareholders_equity')
        
        # 资产负债率
        if total_assets and total_debt and total_assets != 0:
            ratios['debt_to_assets'] = total_debt / total_assets
        
        # 负债权益比
        if shareholders_equity and total_debt and shareholders_equity != 0:
            ratios['debt_to_equity'] = total_debt / shareholders_equity
        
        # 流动比率
        if current_liabilities and current_assets and current_liabilities != 0:
            ratios['current_ratio'] = current_assets / current_liabilities
        
        return ratios
    
    def calculate_efficiency_ratios(self, record):
        """计算营运能力指标"""
        ratios = {}
        
        revenue = record.get('revenue')
        total_assets = record.get('total_assets')
        shareholders_equity = record.get('shareholders_equity')
        
        # 总资产周转率 (季度营收 * 4 / 总资产)
        if total_assets and revenue and total_assets != 0:
            ratios['asset_turnover'] = (revenue * 4) / total_assets
        
        # 净资产周转率 (季度营收 * 4 / 净资产)
        if shareholders_equity and revenue and shareholders_equity != 0:
            ratios['equity_turnover'] = (revenue * 4) / shareholders_equity
        
        return ratios
    
    def calculate_growth_ratios(self, stock_code, current_date, record):
        """计算成长能力指标（同比增长率）"""
        ratios = {}
        
        try:
            conn = sqlite3.connect(self.db_name)
            
            # 计算去年同期日期
            current_year = int(current_date[:4])
            same_quarter_last_year = current_date.replace(str(current_year), str(current_year - 1))
            
            # 获取去年同期数据
            query = """
            SELECT revenue, net_income, shareholders_equity
            FROM financial_data 
            WHERE stock_code = ? AND report_date = ?
            """
            
            cursor = conn.cursor()
            last_year_data = cursor.execute(query, (stock_code, same_quarter_last_year)).fetchone()
            conn.close()
            
            if last_year_data:
                last_revenue, last_net_income, last_equity = last_year_data
                
                current_revenue = record.get('revenue')
                current_net_income = record.get('net_income')
                current_equity = record.get('shareholders_equity')
                
                # 营收同比增长率
                if last_revenue and current_revenue and last_revenue != 0:
                    ratios['revenue_growth_yoy'] = (current_revenue - last_revenue) / last_revenue
                
                # 净利润同比增长率
                if last_net_income and current_net_income and last_net_income != 0:
                    ratios['net_income_growth_yoy'] = (current_net_income - last_net_income) / last_net_income
                
                # 净资产同比增长率
                if last_equity and current_equity and last_equity != 0:
                    ratios['equity_growth_yoy'] = (current_equity - last_equity) / last_equity
            
        except Exception as e:
            logger.warning(f"计算{stock_code}成长指标失败: {e}")
        
        return ratios
    
    def calculate_per_share_ratios(self, record):
        """计算每股指标"""
        ratios = {}
        
        # 从原始数据中获取每股指标
        ratios['eps'] = record.get('eps')
        ratios['book_value_per_share'] = record.get('book_value_per_share')
        
        # 如果有股本数据，可以计算每股营收
        # 这里暂时跳过，因为需要额外的股本数据
        
        return ratios

    def calculate_ratios_for_record(self, stock_code, record):
        """为单条记录计算所有财务比率"""
        try:
            ratios = {
                'stock_code': stock_code,
                'report_date': record['report_date'],
                'period_type': record['period_type']
            }

            # 计算各类指标
            profitability = self.calculate_profitability_ratios(record)
            solvency = self.calculate_solvency_ratios(record)
            efficiency = self.calculate_efficiency_ratios(record)
            growth = self.calculate_growth_ratios(stock_code, record['report_date'], record)
            per_share = self.calculate_per_share_ratios(record)

            # 合并所有指标
            ratios.update(profitability)
            ratios.update(solvency)
            ratios.update(efficiency)
            ratios.update(growth)
            ratios.update(per_share)

            # 计算ROE-TTM
            roe_ttm = self.calculate_roe_ttm(
                stock_code,
                record['report_date'],
                record.get('shareholders_equity')
            )
            if roe_ttm is not None:
                ratios['roe_ttm'] = roe_ttm

            return ratios

        except Exception as e:
            logger.error(f"计算{stock_code}财务比率失败: {e}")
            return None

    def save_ratios(self, ratios_list):
        """保存财务比率到数据库"""
        try:
            conn = sqlite3.connect(self.db_name)
            cursor = conn.cursor()

            for ratios in ratios_list:
                cursor.execute('''
                    INSERT OR REPLACE INTO financial_ratios
                    (stock_code, report_date, period_type, roe, roe_ttm, roa, roic,
                     gross_margin, operating_margin, net_margin, debt_to_equity, debt_to_assets,
                     current_ratio, quick_ratio, asset_turnover, equity_turnover,
                     revenue_growth_yoy, net_income_growth_yoy, equity_growth_yoy,
                     eps, book_value_per_share, revenue_per_share)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    ratios['stock_code'], ratios['report_date'], ratios['period_type'],
                    ratios.get('roe'), ratios.get('roe_ttm'), ratios.get('roa'), ratios.get('roic'),
                    ratios.get('gross_margin'), ratios.get('operating_margin'), ratios.get('net_margin'),
                    ratios.get('debt_to_equity'), ratios.get('debt_to_assets'),
                    ratios.get('current_ratio'), ratios.get('quick_ratio'),
                    ratios.get('asset_turnover'), ratios.get('equity_turnover'),
                    ratios.get('revenue_growth_yoy'), ratios.get('net_income_growth_yoy'), ratios.get('equity_growth_yoy'),
                    ratios.get('eps'), ratios.get('book_value_per_share'), ratios.get('revenue_per_share')
                ))

            conn.commit()
            conn.close()
            logger.info(f"成功保存 {len(ratios_list)} 条财务比率记录")

        except Exception as e:
            logger.error(f"保存财务比率失败: {e}")

    def calculate_stock_ratios(self, stock_code):
        """计算单只股票的所有财务比率"""
        try:
            conn = sqlite3.connect(self.db_name)

            # 获取该股票的所有财务数据
            query = """
            SELECT * FROM financial_data
            WHERE stock_code = ?
            ORDER BY report_date DESC
            """

            df = pd.read_sql_query(query, conn, params=(stock_code,))
            conn.close()

            if df.empty:
                logger.warning(f"{stock_code}: 没有财务数据")
                return

            ratios_list = []
            for _, record in df.iterrows():
                ratios = self.calculate_ratios_for_record(stock_code, record.to_dict())
                if ratios:
                    ratios_list.append(ratios)

            if ratios_list:
                self.save_ratios(ratios_list)
                logger.info(f"✅ {stock_code}: 成功计算 {len(ratios_list)} 条财务比率")

        except Exception as e:
            logger.error(f"计算{stock_code}财务比率失败: {e}")

    def calculate_all_ratios(self):
        """计算所有股票的财务比率"""
        try:
            conn = sqlite3.connect(self.db_name)

            # 获取所有有财务数据的股票
            query = "SELECT DISTINCT stock_code FROM financial_data ORDER BY stock_code"
            stock_codes = pd.read_sql_query(query, conn)['stock_code'].tolist()
            conn.close()

            logger.info(f"开始计算 {len(stock_codes)} 只股票的财务比率")

            for i, stock_code in enumerate(stock_codes, 1):
                logger.info(f"[{i}/{len(stock_codes)}] 处理 {stock_code}")
                self.calculate_stock_ratios(stock_code)

            logger.info("✅ 完成所有股票财务比率计算")

        except Exception as e:
            logger.error(f"批量计算财务比率失败: {e}")

    def verify_calculations(self, stock_code='00700', limit=5):
        """验证计算结果"""
        try:
            conn = sqlite3.connect(self.db_name)

            query = """
            SELECT r.report_date,
                   f.revenue/1e9 as revenue_billion,
                   f.net_income/1e9 as income_billion,
                   f.shareholders_equity/1e12 as equity_trillion,
                   r.roe*100 as roe_percent,
                   r.roe_ttm*100 as roe_ttm_percent,
                   r.roa*100 as roa_percent,
                   r.gross_margin*100 as gross_margin_percent,
                   r.debt_to_assets*100 as debt_ratio_percent
            FROM financial_ratios r
            JOIN financial_data f ON r.stock_code = f.stock_code AND r.report_date = f.report_date
            WHERE r.stock_code = ?
            ORDER BY r.report_date DESC
            LIMIT ?
            """

            df = pd.read_sql_query(query, conn, params=(stock_code, limit))
            conn.close()

            logger.info(f"=== {stock_code} 财务比率验证 ===")
            for _, row in df.iterrows():
                logger.info(f"报告期: {row['report_date']}")
                logger.info(f"  营收: {row['revenue_billion']:.1f} 十亿元")
                logger.info(f"  净利润: {row['income_billion']:.1f} 十亿元")
                logger.info(f"  股东权益: {row['equity_trillion']:.3f} 万亿元")
                logger.info(f"  ROE(单季): {row['roe_percent']:.2f}%" if pd.notna(row['roe_percent']) else "  ROE(单季): None")
                logger.info(f"  ROE-TTM: {row['roe_ttm_percent']:.2f}%" if pd.notna(row['roe_ttm_percent']) else "  ROE-TTM: None")
                logger.info(f"  ROA: {row['roa_percent']:.2f}%" if pd.notna(row['roa_percent']) else "  ROA: None")
                logger.info(f"  毛利率: {row['gross_margin_percent']:.2f}%" if pd.notna(row['gross_margin_percent']) else "  毛利率: None")
                logger.info(f"  资产负债率: {row['debt_ratio_percent']:.2f}%" if pd.notna(row['debt_ratio_percent']) else "  资产负债率: None")
                logger.info("-" * 40)

        except Exception as e:
            logger.error(f"验证计算结果失败: {e}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='计算财务指标')
    parser.add_argument('--db-name', default='ganggutong_financial_data.db', help='数据库文件名')
    parser.add_argument('--stock-code', help='指定股票代码（可选）')
    parser.add_argument('--verify', action='store_true', help='验证计算结果')

    args = parser.parse_args()

    calculator = FinancialRatiosCalculator(args.db_name)

    if args.verify:
        stock_code = args.stock_code or '00700'
        calculator.verify_calculations(stock_code)
    else:
        if args.stock_code:
            calculator.calculate_stock_ratios(args.stock_code)
            calculator.verify_calculations(args.stock_code)
        else:
            calculator.calculate_all_ratios()
            calculator.verify_calculations('00700')  # 验证腾讯的结果

if __name__ == "__main__":
    main()
