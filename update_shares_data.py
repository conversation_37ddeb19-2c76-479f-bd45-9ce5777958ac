#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
更新现有财务数据的股本信息
"""

import sqlite3
import pandas as pd
import logging
from shares_data_reference import get_shares_data

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('update_shares_data.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def update_shares_data_for_existing_records():
    """为现有的财务数据记录更新股本信息"""
    try:
        conn = sqlite3.connect('ganggutong_financial_data.db')
        
        # 获取所有有财务数据但没有股本数据的记录
        query = """
        SELECT DISTINCT stock_code 
        FROM financial_data 
        WHERE shares_outstanding IS NULL OR total_shares IS NULL
        ORDER BY stock_code
        """
        
        stock_codes = pd.read_sql_query(query, conn)['stock_code'].tolist()
        logger.info(f"发现 {len(stock_codes)} 只股票需要更新股本数据")
        
        cursor = conn.cursor()
        updated_count = 0
        
        for stock_code in stock_codes:
            # 从参考数据获取股本信息
            shares_data = get_shares_data(stock_code)
            
            if shares_data:
                shares_outstanding = shares_data['shares_outstanding']
                total_shares = shares_data['total_shares']
                
                # 更新该股票的所有记录
                cursor.execute("""
                    UPDATE financial_data 
                    SET shares_outstanding = ?, total_shares = ?
                    WHERE stock_code = ?
                """, (shares_outstanding, total_shares, stock_code))
                
                affected_rows = cursor.rowcount
                updated_count += affected_rows
                
                logger.info(f"✅ {stock_code}: 更新 {affected_rows} 条记录的股本数据 "
                          f"(流通股本: {shares_outstanding/1e9:.2f}十亿股)")
            else:
                logger.warning(f"⚠️ {stock_code}: 没有找到股本参考数据")
        
        conn.commit()
        conn.close()
        
        logger.info(f"✅ 股本数据更新完成，总共更新 {updated_count} 条记录")
        
    except Exception as e:
        logger.error(f"更新股本数据失败: {e}")

def verify_shares_data():
    """验证股本数据更新结果"""
    try:
        conn = sqlite3.connect('ganggutong_financial_data.db')
        
        # 检查有股本数据的记录数
        query = """
        SELECT 
            COUNT(*) as total_records,
            COUNT(shares_outstanding) as records_with_shares,
            COUNT(DISTINCT stock_code) as total_stocks,
            COUNT(DISTINCT CASE WHEN shares_outstanding IS NOT NULL THEN stock_code END) as stocks_with_shares
        FROM financial_data
        """
        
        result = pd.read_sql_query(query, conn).iloc[0]
        
        logger.info("=== 股本数据验证结果 ===")
        logger.info(f"总财务记录数: {result['total_records']}")
        logger.info(f"有股本数据的记录数: {result['records_with_shares']}")
        logger.info(f"总股票数: {result['total_stocks']}")
        logger.info(f"有股本数据的股票数: {result['stocks_with_shares']}")
        logger.info(f"股本数据覆盖率: {result['records_with_shares']/result['total_records']*100:.1f}%")
        
        # 显示几个有股本数据的例子
        query = """
        SELECT stock_code, report_date, 
               shares_outstanding/1e9 as shares_billion,
               revenue/1e9 as revenue_billion,
               net_income/1e9 as income_billion
        FROM financial_data 
        WHERE shares_outstanding IS NOT NULL
        ORDER BY stock_code, report_date DESC
        LIMIT 10
        """
        
        examples = pd.read_sql_query(query, conn)
        
        logger.info("\n=== 股本数据示例 ===")
        for _, row in examples.iterrows():
            logger.info(f"{row['stock_code']} {row['report_date']}: "
                      f"股本 {row['shares_billion']:.2f}十亿股, "
                      f"营收 {row['revenue_billion']:.1f}十亿元")
        
        conn.close()
        
    except Exception as e:
        logger.error(f"验证股本数据失败: {e}")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='更新股本数据')
    parser.add_argument('--verify', action='store_true', help='只验证股本数据，不进行更新')
    
    args = parser.parse_args()
    
    if args.verify:
        verify_shares_data()
    else:
        update_shares_data_for_existing_records()
        verify_shares_data()

if __name__ == "__main__":
    main()
