#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析ganggutong_financial_data.db中net_income列空值问题
"""

import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime

def analyze_net_income_nulls():
    """分析net_income列的空值情况"""
    
    # 连接数据库
    conn = sqlite3.connect('ganggutong_financial_data.db')
    
    try:
        print("🔍 分析net_income列空值情况...")
        print("=" * 60)
        
        # 1. 总体统计
        print("\n📊 总体统计:")
        total_query = """
        SELECT 
            COUNT(*) as total_records,
            COUNT(net_income) as non_null_net_income,
            COUNT(*) - COUNT(net_income) as null_net_income,
            ROUND((COUNT(*) - COUNT(net_income)) * 100.0 / COUNT(*), 2) as null_percentage
        FROM financial_data
        """
        
        total_stats = pd.read_sql_query(total_query, conn)
        print(f"   总记录数: {total_stats['total_records'].iloc[0]:,}")
        print(f"   net_income非空: {total_stats['non_null_net_income'].iloc[0]:,}")
        print(f"   net_income为空: {total_stats['null_net_income'].iloc[0]:,}")
        print(f"   空值比例: {total_stats['null_percentage'].iloc[0]}%")
        
        # 2. 按股票代码统计
        print("\n📈 按股票代码统计空值情况:")
        stock_query = """
        SELECT 
            stock_code,
            COUNT(*) as total_records,
            COUNT(net_income) as non_null_count,
            COUNT(*) - COUNT(net_income) as null_count,
            ROUND((COUNT(*) - COUNT(net_income)) * 100.0 / COUNT(*), 2) as null_percentage
        FROM financial_data
        GROUP BY stock_code
        HAVING null_count > 0
        ORDER BY null_percentage DESC, null_count DESC
        LIMIT 20
        """
        
        stock_stats = pd.read_sql_query(stock_query, conn)
        if not stock_stats.empty:
            print("   空值最多的前20只股票:")
            for _, row in stock_stats.iterrows():
                print(f"   {row['stock_code']}: {row['null_count']}/{row['total_records']} ({row['null_percentage']}%)")
        else:
            print("   所有股票的net_income都有数据")
        
        # 3. 按时间统计
        print("\n📅 按报告期统计空值情况:")
        time_query = """
        SELECT 
            report_date,
            COUNT(*) as total_records,
            COUNT(net_income) as non_null_count,
            COUNT(*) - COUNT(net_income) as null_count,
            ROUND((COUNT(*) - COUNT(net_income)) * 100.0 / COUNT(*), 2) as null_percentage
        FROM financial_data
        GROUP BY report_date
        HAVING null_count > 0
        ORDER BY report_date DESC
        LIMIT 15
        """
        
        time_stats = pd.read_sql_query(time_query, conn)
        if not time_stats.empty:
            print("   空值最多的报告期:")
            for _, row in time_stats.iterrows():
                print(f"   {row['report_date']}: {row['null_count']}/{row['total_records']} ({row['null_percentage']}%)")
        
        # 4. 按期间类型统计
        print("\n📋 按期间类型统计:")
        period_query = """
        SELECT 
            period_type,
            COUNT(*) as total_records,
            COUNT(net_income) as non_null_count,
            COUNT(*) - COUNT(net_income) as null_count,
            ROUND((COUNT(*) - COUNT(net_income)) * 100.0 / COUNT(*), 2) as null_percentage
        FROM financial_data
        GROUP BY period_type
        """
        
        period_stats = pd.read_sql_query(period_query, conn)
        for _, row in period_stats.iterrows():
            print(f"   {row['period_type']}: {row['null_count']}/{row['total_records']} ({row['null_percentage']}%)")
        
        # 5. 检查其他财务指标的空值情况
        print("\n💰 其他财务指标空值情况:")
        financial_fields = ['revenue', 'gross_profit', 'operating_income', 'net_income', 
                          'total_assets', 'total_debt', 'shareholders_equity', 
                          'operating_cash_flow', 'free_cash_flow', 'eps']
        
        for field in financial_fields:
            field_query = f"""
            SELECT 
                COUNT(*) - COUNT({field}) as null_count,
                ROUND((COUNT(*) - COUNT({field})) * 100.0 / COUNT(*), 2) as null_percentage
            FROM financial_data
            """
            field_stats = pd.read_sql_query(field_query, conn)
            null_count = field_stats['null_count'].iloc[0]
            null_pct = field_stats['null_percentage'].iloc[0]
            print(f"   {field}: {null_count:,} ({null_pct}%)")
        
        # 6. 查看具体的空值记录样本
        print("\n🔍 net_income为空的记录样本:")
        sample_query = """
        SELECT stock_code, report_date, period_type, revenue, gross_profit, 
               operating_income, net_income, total_assets
        FROM financial_data
        WHERE net_income IS NULL
        ORDER BY report_date DESC
        LIMIT 10
        """
        
        sample_data = pd.read_sql_query(sample_query, conn)
        if not sample_data.empty:
            print(sample_data.to_string(index=False))
        
        # 7. 检查是否有其他数据但net_income为空的情况
        print("\n⚠️  有其他财务数据但net_income为空的记录:")
        partial_query = """
        SELECT stock_code, report_date, period_type, revenue, gross_profit, 
               operating_income, net_income, total_assets
        FROM financial_data
        WHERE net_income IS NULL 
        AND (revenue IS NOT NULL OR gross_profit IS NOT NULL OR operating_income IS NOT NULL)
        ORDER BY report_date DESC
        LIMIT 10
        """
        
        partial_data = pd.read_sql_query(partial_query, conn)
        if not partial_data.empty:
            print("   发现有其他数据但net_income为空的记录:")
            print(partial_data.to_string(index=False))
        else:
            print("   没有发现有其他数据但net_income为空的记录")
        
        # 8. 检查下载日志
        print("\n📥 检查下载状态:")
        download_query = """
        SELECT 
            status,
            COUNT(*) as count,
            COUNT(DISTINCT stock_code) as unique_stocks
        FROM download_log
        WHERE data_type = 'financial'
        GROUP BY status
        """
        
        download_stats = pd.read_sql_query(download_query, conn)
        if not download_stats.empty:
            for _, row in download_stats.iterrows():
                print(f"   {row['status']}: {row['count']} 次下载, {row['unique_stocks']} 只股票")
        
        # 9. 检查失败的下载
        print("\n❌ 下载失败的记录:")
        failed_query = """
        SELECT stock_code, error_message, download_time
        FROM download_log
        WHERE data_type = 'financial' AND status = 'failed'
        ORDER BY download_time DESC
        LIMIT 10
        """
        
        failed_data = pd.read_sql_query(failed_query, conn)
        if not failed_data.empty:
            print("   最近的失败记录:")
            for _, row in failed_data.iterrows():
                print(f"   {row['stock_code']}: {row['error_message']} ({row['download_time']})")
        else:
            print("   没有发现下载失败的记录")
        
    except Exception as e:
        print(f"❌ 分析过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        conn.close()

def suggest_solutions():
    """提供解决方案建议"""
    print("\n💡 可能的原因和解决方案:")
    print("=" * 60)
    
    print("\n🔍 可能的原因:")
    print("1. 数据源问题:")
    print("   - Yahoo Finance或其他数据源没有提供net_income数据")
    print("   - 某些股票的财务数据不完整")
    print("   - 数据源的字段映射问题")
    
    print("\n2. 数据获取问题:")
    print("   - 网络请求失败或超时")
    print("   - API限制或访问被拒绝")
    print("   - 数据解析错误")
    
    print("\n3. 数据处理问题:")
    print("   - 字段名称不匹配")
    print("   - 数据类型转换问题")
    print("   - 空值处理逻辑问题")
    
    print("\n🛠️  建议的解决方案:")
    print("1. 检查数据获取脚本:")
    print("   - 验证数据源API是否正常")
    print("   - 检查字段映射是否正确")
    print("   - 添加更详细的错误日志")
    
    print("\n2. 数据源多样化:")
    print("   - 尝试多个数据源")
    print("   - 实现数据源切换机制")
    print("   - 添加数据验证和清洗")
    
    print("\n3. 重新获取数据:")
    print("   - 针对空值记录重新下载")
    print("   - 使用不同的时间间隔重试")
    print("   - 手动验证部分数据")

def main():
    """主函数"""
    print("🎯 ganggutong_financial_data.db net_income空值分析")
    print("=" * 60)
    
    # 检查数据库文件是否存在
    import os
    if not os.path.exists('ganggutong_financial_data.db'):
        print("❌ 数据库文件 ganggutong_financial_data.db 不存在")
        return
    
    # 分析空值情况
    analyze_net_income_nulls()
    
    # 提供解决方案建议
    suggest_solutions()
    
    print(f"\n✅ 分析完成 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
