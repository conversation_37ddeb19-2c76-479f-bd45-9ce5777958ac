#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试净利润修复效果
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ganggutong_financial_downloader import GangguTongFinancialDownloader
import sqlite3
import pandas as pd
from datetime import datetime

def test_single_stock_extraction():
    """测试单只股票的数据提取"""
    print("🧪 测试单只股票数据提取...")
    
    # 创建下载器实例
    downloader = GangguTongFinancialDownloader(
        db_name='test_net_income.db',
        csv_file='data_files/ganggutong_consti.csv'
    )
    
    # 测试腾讯
    stock_code = "00700"
    stock_name = "腾讯控股"
    
    print(f"   测试股票: {stock_code} ({stock_name})")
    
    # 使用东方财富网API获取数据
    financial_records = downloader.get_eastmoney_financial_data(stock_code, stock_name)
    
    if financial_records:
        print(f"   ✅ 成功提取 {len(financial_records)} 条财务记录")
        
        # 显示前几条记录
        for i, record in enumerate(financial_records[:3]):
            print(f"\n   记录 {i+1}:")
            print(f"     报告期: {record.get('report_date')}")
            print(f"     营收: {record.get('revenue', 'N/A'):,} 元" if record.get('revenue') else "     营收: N/A")
            print(f"     净利润: {record.get('net_income', 'N/A'):,} 元" if record.get('net_income') else "     净利润: N/A")
            
            if record.get('revenue') and record.get('net_income'):
                net_margin = record['net_income'] / record['revenue']
                print(f"     净利润率: {net_margin:.2%}")
        
        # 统计有净利润数据的记录
        net_income_count = sum(1 for record in financial_records if record.get('net_income'))
        print(f"\n   📊 统计:")
        print(f"     总记录数: {len(financial_records)}")
        print(f"     有净利润数据: {net_income_count}")
        print(f"     净利润覆盖率: {net_income_count/len(financial_records)*100:.1f}%")
        
    else:
        print("   ❌ 未能提取到财务数据")
    
    # 清理测试数据库
    try:
        os.remove('test_net_income.db')
    except:
        pass

def test_multiple_stocks():
    """测试多只股票的数据提取"""
    print("\n🧪 测试多只股票数据提取...")
    
    # 创建下载器实例
    downloader = GangguTongFinancialDownloader(
        db_name='test_multiple.db',
        csv_file='data_files/ganggutong_consti.csv'
    )
    
    # 测试几只知名股票
    test_stocks = [
        ("00700", "腾讯控股"),
        ("00941", "中国移动"),
        ("01299", "友邦保险"),
        ("02318", "中国平安"),
        ("03690", "美团-W")
    ]
    
    results = {}
    
    for stock_code, stock_name in test_stocks:
        print(f"\n   测试 {stock_code} ({stock_name})...")
        
        financial_records = downloader.get_eastmoney_financial_data(stock_code, stock_name)
        
        if financial_records:
            net_income_count = sum(1 for record in financial_records if record.get('net_income'))
            results[stock_code] = {
                'name': stock_name,
                'total_records': len(financial_records),
                'net_income_records': net_income_count,
                'coverage': net_income_count/len(financial_records)*100 if financial_records else 0
            }
            print(f"     ✅ {len(financial_records)} 条记录，{net_income_count} 条有净利润 ({results[stock_code]['coverage']:.1f}%)")
        else:
            results[stock_code] = {
                'name': stock_name,
                'total_records': 0,
                'net_income_records': 0,
                'coverage': 0
            }
            print(f"     ❌ 无数据")
    
    # 显示汇总结果
    print(f"\n   📊 汇总结果:")
    for stock_code, result in results.items():
        print(f"     {stock_code} ({result['name']}): {result['net_income_records']}/{result['total_records']} ({result['coverage']:.1f}%)")
    
    # 计算总体覆盖率
    total_records = sum(r['total_records'] for r in results.values())
    total_net_income = sum(r['net_income_records'] for r in results.values())
    overall_coverage = total_net_income/total_records*100 if total_records > 0 else 0
    
    print(f"\n   🎯 总体覆盖率: {total_net_income}/{total_records} ({overall_coverage:.1f}%)")
    
    # 清理测试数据库
    try:
        os.remove('test_multiple.db')
    except:
        pass

def compare_before_after():
    """对比修复前后的效果"""
    print("\n🔄 对比修复前后效果...")
    
    # 检查当前数据库的状态
    try:
        conn = sqlite3.connect('ganggutong_financial_data.db')
        
        # 统计当前的净利润覆盖率
        query = """
        SELECT 
            COUNT(*) as total_records,
            COUNT(net_income) as net_income_records,
            ROUND(COUNT(net_income) * 100.0 / COUNT(*), 2) as coverage_rate
        FROM financial_data
        """
        
        current_stats = pd.read_sql_query(query, conn)
        conn.close()
        
        print(f"   当前数据库状态:")
        print(f"     总记录数: {current_stats['total_records'].iloc[0]:,}")
        print(f"     有净利润记录: {current_stats['net_income_records'].iloc[0]:,}")
        print(f"     覆盖率: {current_stats['coverage_rate'].iloc[0]}%")
        
        if current_stats['coverage_rate'].iloc[0] < 50:
            print(f"   ⚠️  覆盖率仍然很低，建议重新下载数据")
        else:
            print(f"   ✅ 覆盖率已改善")
            
    except Exception as e:
        print(f"   ❌ 无法访问数据库: {e}")

def suggest_next_steps():
    """建议下一步操作"""
    print(f"\n💡 建议下一步操作:")
    print(f"   1. 如果测试结果显示净利润提取正常，建议重新下载所有数据:")
    print(f"      python ganggutong_financial_downloader.py")
    print(f"   ")
    print(f"   2. 或者只重新下载部分股票进行验证:")
    print(f"      python ganggutong_financial_downloader.py --max-stocks 10")
    print(f"   ")
    print(f"   3. 下载完成后检查结果:")
    print(f"      python analyze_net_income.py")
    print(f"   ")
    print(f"   4. 如果仍有问题，可以查看详细的下载日志")

def main():
    """主函数"""
    print("🎯 测试净利润修复效果")
    print("=" * 60)
    
    # 1. 测试单只股票
    test_single_stock_extraction()
    
    # 2. 测试多只股票
    test_multiple_stocks()
    
    # 3. 对比修复前后
    compare_before_after()
    
    # 4. 建议下一步操作
    suggest_next_steps()
    
    print(f"\n✅ 测试完成 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
