#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试API响应结构
"""

import requests
import json
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ganggutong_financial_downloader import GangguTongFinancialDownloader

def debug_api_response():
    """调试API响应"""
    print("🔍 调试API响应结构...")
    
    stock_code = "00700"
    formatted_code = f"{stock_code}.HK"
    
    url = f"https://datacenter.eastmoney.com/securities/api/data/v1/get?reportName=RPT_HKF10_FN_INCOME_PC&columns=ALL&quoteColumns=&filter=(SECUCODE%3D%22{formatted_code}%22)&pageNumber=1&pageSize=200&sortTypes=-1&sortColumns=REPORT_DATE&source=F10&client=PC"
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Referer': 'https://datacenter.eastmoney.com/',
        'Accept': 'application/json, text/javascript, */*; q=0.01'
    }
    
    try:
        print(f"   请求URL: {url}")
        response = requests.get(url, headers=headers, timeout=30)
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            try:
                data = response.json()
                print(f"   响应类型: {type(data)}")
                print(f"   响应键: {list(data.keys()) if isinstance(data, dict) else 'Not a dict'}")
                
                # 检查响应结构
                print(f"\n   检查响应结构:")
                print(f"     'result' in data: {'result' in data if isinstance(data, dict) else False}")
                
                if isinstance(data, dict) and 'result' in data:
                    result = data['result']
                    print(f"     data['result'] type: {type(result)}")
                    print(f"     data['result'] is None: {result is None}")
                    
                    if isinstance(result, dict):
                        print(f"     data['result'] keys: {list(result.keys())}")
                        print(f"     'data' in data['result']: {'data' in result}")
                        
                        if 'data' in result:
                            result_data = result['data']
                            print(f"     data['result']['data'] type: {type(result_data)}")
                            print(f"     data['result']['data'] length: {len(result_data) if result_data else 0}")
                            print(f"     data['result']['data'] is empty: {not result_data}")
                
                # 显示完整的响应结构（前几层）
                print(f"\n   完整响应结构:")
                print(json.dumps(data, indent=2, ensure_ascii=False)[:1000] + "..." if len(str(data)) > 1000 else json.dumps(data, indent=2, ensure_ascii=False))
                
            except json.JSONDecodeError as e:
                print(f"   JSON解析失败: {e}")
                print(f"   响应内容: {response.text[:500]}")
        else:
            print(f"   请求失败: {response.text[:200]}")
            
    except Exception as e:
        print(f"   请求异常: {e}")

def debug_downloader_method():
    """调试下载器方法"""
    print(f"\n🔍 调试下载器方法...")
    
    # 创建下载器实例
    downloader = GangguTongFinancialDownloader(
        db_name='debug_test.db',
        csv_file='data_files/ganggutong_consti.csv'
    )
    
    stock_code = "00700"
    stock_name = "腾讯控股"
    
    print(f"   测试股票: {stock_code} ({stock_name})")
    
    # 直接调用get_eastmoney_financial_data方法
    try:
        financial_records = downloader.get_eastmoney_financial_data(stock_code, stock_name)
        print(f"   返回记录数: {len(financial_records) if financial_records else 0}")
        
        if financial_records:
            print(f"   第一条记录: {financial_records[0]}")
        
    except Exception as e:
        print(f"   方法调用失败: {e}")
        import traceback
        traceback.print_exc()
    
    # 清理测试数据库
    try:
        os.remove('debug_test.db')
    except:
        pass

def compare_methods():
    """对比不同的调用方法"""
    print(f"\n🔄 对比不同调用方法...")
    
    stock_code = "00700"
    formatted_code = f"{stock_code}.HK"
    
    # 方法1: 直接API调用
    print(f"   方法1: 直接API调用")
    url = f"https://datacenter.eastmoney.com/securities/api/data/v1/get?reportName=RPT_HKF10_FN_INCOME_PC&columns=ALL&quoteColumns=&filter=(SECUCODE%3D%22{formatted_code}%22)&pageNumber=1&pageSize=200&sortTypes=-1&sortColumns=REPORT_DATE&source=F10&client=PC"
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Referer': 'https://datacenter.eastmoney.com/',
        'Accept': 'application/json, text/javascript, */*; q=0.01'
    }
    
    try:
        response = requests.get(url, headers=headers, timeout=30)
        if response.status_code == 200:
            data = response.json()
            if 'result' in data and 'data' in data['result'] and data['result']['data']:
                print(f"     ✅ 成功获取 {len(data['result']['data'])} 条记录")
            else:
                print(f"     ❌ 数据结构异常")
        else:
            print(f"     ❌ 请求失败: {response.status_code}")
    except Exception as e:
        print(f"     ❌ 异常: {e}")
    
    # 方法2: 使用下载器类
    print(f"   方法2: 使用下载器类")
    try:
        downloader = GangguTongFinancialDownloader(
            db_name='debug_test2.db',
            csv_file='data_files/ganggutong_consti.csv'
        )
        
        financial_records = downloader.get_eastmoney_financial_data(stock_code, "腾讯控股")
        if financial_records:
            print(f"     ✅ 成功获取 {len(financial_records)} 条记录")
        else:
            print(f"     ❌ 未获取到数据")
            
        # 清理
        os.remove('debug_test2.db')
    except Exception as e:
        print(f"     ❌ 异常: {e}")

def main():
    """主函数"""
    print("🎯 调试API响应和下载器")
    print("=" * 60)
    
    # 1. 调试API响应
    debug_api_response()
    
    # 2. 调试下载器方法
    debug_downloader_method()
    
    # 3. 对比不同方法
    compare_methods()
    
    print(f"\n✅ 调试完成")

if __name__ == "__main__":
    main()
