#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复腾讯股东权益数据的脚本
重新获取正确的股东权益数据（排除少数股东权益）
"""

import sqlite3
import requests
import pandas as pd
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('fix_tencent_equity.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class TencentEquityFixer:
    """腾讯股东权益数据修复器"""
    
    def __init__(self, db_name='ganggutong_financial_data.db'):
        self.db_name = db_name
        
    def get_correct_equity_data(self, stock_code='00700'):
        """从东方财富API获取正确的股东权益数据"""
        try:
            formatted_code = f'{stock_code}.HK'
            balance_url = f'https://datacenter.eastmoney.com/securities/api/data/v1/get?reportName=RPT_HKF10_FN_BALANCE_PC&columns=ALL&quoteColumns=&filter=(SECUCODE%3D%22{formatted_code}%22)&pageNumber=1&pageSize=50&sortTypes=-1&sortColumns=REPORT_DATE&source=F10&client=PC'
            
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'Referer': 'https://datacenter.eastmoney.com/',
            }
            
            response = requests.get(balance_url, headers=headers, timeout=30)
            if response.status_code == 200:
                data = response.json()
                if data.get('success') and data.get('result') and data['result'].get('data'):
                    balance_data = data['result']['data']
                    
                    # 按报告期分组查找正确的股东权益
                    equity_data = {}
                    for item in balance_data:
                        report_date = item.get('REPORT_DATE', '')[:10]
                        item_name = item.get('STD_ITEM_NAME', '')
                        amount = item.get('AMOUNT')
                        
                        if report_date not in equity_data:
                            equity_data[report_date] = {}
                        
                        # 优先查找总权益（最全面的股东权益）
                        if item_name == '总权益':
                            equity_data[report_date]['total_equity'] = float(amount) if amount else None
                            equity_data[report_date]['best_equity_name'] = item_name

                        # 查找净资产
                        elif item_name == '净资产':
                            equity_data[report_date]['net_assets'] = float(amount) if amount else None
                            if 'best_equity_name' not in equity_data[report_date]:
                                equity_data[report_date]['best_equity_name'] = item_name

                        # 查找股东权益（排除少数股东权益）
                        elif '股东权益' in item_name and '少数' not in item_name:
                            equity_data[report_date]['shareholders_equity'] = float(amount) if amount else None
                            if 'best_equity_name' not in equity_data[report_date]:
                                equity_data[report_date]['best_equity_name'] = item_name
                        
                        # 查找少数股东权益
                        elif '少数股东权益' in item_name:
                            equity_data[report_date]['minority_equity'] = float(amount) if amount else None
                    
                    return equity_data
                    
            return {}
            
        except Exception as e:
            logger.error(f"获取股东权益数据失败: {e}")
            return {}
    
    def compare_and_fix_data(self, stock_code='00700'):
        """比较并修复数据库中的股东权益数据"""
        try:
            # 获取正确的API数据
            api_data = self.get_correct_equity_data(stock_code)
            if not api_data:
                logger.error("无法获取API数据")
                return
            
            # 连接数据库
            conn = sqlite3.connect(self.db_name)
            cursor = conn.cursor()
            
            # 获取数据库中的数据
            query = """
            SELECT report_date, shareholders_equity, net_income, roe
            FROM financial_data 
            WHERE stock_code = ? 
            ORDER BY report_date DESC
            """
            
            db_data = pd.read_sql_query(query, conn, params=(stock_code,))
            
            logger.info("=== 腾讯股东权益数据对比 ===")
            
            fixed_count = 0
            for _, row in db_data.iterrows():
                report_date = row['report_date']
                db_equity = row['shareholders_equity']
                db_net_income = row['net_income']
                db_roe = row['roe']
                
                if report_date in api_data:
                    api_equity = api_data[report_date].get('shareholders_equity')
                    total_equity = api_data[report_date].get('total_equity')
                    net_assets = api_data[report_date].get('net_assets')
                    minority_equity = api_data[report_date].get('minority_equity')
                    
                    # 选择最合适的股东权益值（优先使用总权益）
                    correct_equity = total_equity or net_assets or api_equity
                    
                    if correct_equity and db_equity:
                        equity_diff = abs(correct_equity - db_equity) / correct_equity
                        
                        logger.info(f"报告期: {report_date}")
                        logger.info(f"  数据库股东权益: {db_equity/1e12:.3f} 万亿元")
                        logger.info(f"  API股东权益: {correct_equity/1e12:.3f} 万亿元")
                        if minority_equity:
                            logger.info(f"  少数股东权益: {minority_equity/1e12:.3f} 万亿元")
                        logger.info(f"  差异: {equity_diff*100:.1f}%")
                        
                        # 如果差异超过5%，则更新数据
                        if equity_diff > 0.05:
                            # 重新计算ROE
                            new_roe = None
                            if db_net_income and correct_equity and correct_equity != 0:
                                roe_value = db_net_income / correct_equity
                                if -1 <= roe_value <= 1:  # 合理范围
                                    new_roe = roe_value
                            
                            logger.info(f"  更新股东权益: {db_equity/1e12:.3f} -> {correct_equity/1e12:.3f} 万亿元")
                            logger.info(f"  更新ROE: {db_roe*100:.2f}% -> {new_roe*100:.2f}%" if new_roe else f"  更新ROE: {db_roe} -> None")
                            
                            # 更新数据库
                            cursor.execute("""
                                UPDATE financial_data 
                                SET shareholders_equity = ?, roe = ?
                                WHERE stock_code = ? AND report_date = ?
                            """, (correct_equity, new_roe, stock_code, report_date))
                            
                            fixed_count += 1
                        
                        logger.info("-" * 50)
            
            conn.commit()
            conn.close()
            
            logger.info(f"✅ 成功修复 {fixed_count} 条记录")
            
        except Exception as e:
            logger.error(f"修复数据失败: {e}")
            if 'conn' in locals():
                conn.rollback()
                conn.close()
    
    def verify_fix(self, stock_code='00700'):
        """验证修复结果"""
        try:
            conn = sqlite3.connect(self.db_name)
            
            # 检查修复后的数据
            query = """
            SELECT report_date, 
                   shareholders_equity/1e12 as equity_trillion,
                   net_income/1e12 as income_trillion,
                   roe*100 as roe_percent
            FROM financial_data 
            WHERE stock_code = ? 
            ORDER BY report_date DESC
            LIMIT 5
            """
            
            df = pd.read_sql_query(query, conn, params=(stock_code,))
            conn.close()
            
            logger.info("=== 修复后腾讯数据验证 ===")
            for _, row in df.iterrows():
                logger.info(f"报告期: {row['report_date']}")
                logger.info(f"  股东权益: {row['equity_trillion']:.3f} 万亿元")
                logger.info(f"  净利润: {row['income_trillion']:.3f} 万亿元")
                logger.info(f"  ROE: {row['roe_percent']:.2f}%" if pd.notna(row['roe_percent']) else "  ROE: None")
                logger.info("-" * 40)
            
        except Exception as e:
            logger.error(f"验证修复结果失败: {e}")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='修复腾讯股东权益数据')
    parser.add_argument('--stock-code', default='00700', help='股票代码')
    parser.add_argument('--db-name', default='ganggutong_financial_data.db', help='数据库文件名')
    parser.add_argument('--verify', action='store_true', help='验证修复结果')
    
    args = parser.parse_args()
    
    fixer = TencentEquityFixer(args.db_name)
    
    if args.verify:
        fixer.verify_fix(args.stock_code)
    else:
        fixer.compare_and_fix_data(args.stock_code)
        fixer.verify_fix(args.stock_code)

if __name__ == "__main__":
    main()
