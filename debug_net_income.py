#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试net_income空值问题 - 检查数据获取过程
"""

import sqlite3
import pandas as pd
import requests
import json
from datetime import datetime
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_eastmoney_api():
    """测试东方财富网API是否能正常获取数据"""
    print("🔍 测试东方财富网API...")
    
    # 选择一个有数据的股票进行测试
    test_stock = "00700"  # 腾讯
    formatted_code = f"{test_stock}.HK"
    
    url = f"https://datacenter.eastmoney.com/securities/api/data/v1/get?reportName=RPT_HKF10_FN_INCOME_PC&columns=ALL&quoteColumns=&filter=(SECUCODE%3D%22{formatted_code}%22)&pageNumber=1&pageSize=200&sortTypes=-1&sortColumns=REPORT_DATE&source=F10&client=PC"
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Referer': 'https://datacenter.eastmoney.com/',
        'Accept': 'application/json, text/javascript, */*; q=0.01'
    }
    
    try:
        response = requests.get(url, headers=headers, timeout=30)
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   响应数据结构: {list(data.keys()) if isinstance(data, dict) else 'Not a dict'}")
            
            if 'result' in data and 'data' in data['result']:
                records = data['result']['data']
                print(f"   获取到 {len(records)} 条记录")
                
                if records:
                    # 查看第一条记录的结构
                    first_record = records[0]
                    print(f"   第一条记录字段: {list(first_record.keys())}")
                    
                    # 查找净利润相关字段
                    net_income_fields = []
                    for key, value in first_record.items():
                        if any(keyword in str(key) for keyword in ['净利润', 'NET', 'PROFIT', 'INCOME']):
                            net_income_fields.append((key, value))
                    
                    print(f"   净利润相关字段: {net_income_fields}")
                    
                    # 查看STD_ITEM_NAME字段的值
                    if 'STD_ITEM_NAME' in first_record:
                        print(f"   STD_ITEM_NAME: {first_record['STD_ITEM_NAME']}")
                    
                    # 查看前几条记录的STD_ITEM_NAME
                    print("\n   前10条记录的项目名称:")
                    for i, record in enumerate(records[:10]):
                        if 'STD_ITEM_NAME' in record and 'AMOUNT' in record:
                            print(f"   {i+1}. {record['STD_ITEM_NAME']}: {record['AMOUNT']}")
                
            else:
                print("   响应数据中没有找到result.data")
        else:
            print(f"   请求失败: {response.text[:200]}")
            
    except Exception as e:
        print(f"   API测试失败: {e}")

def check_database_records():
    """检查数据库中有net_income数据的记录"""
    print("\n🔍 检查数据库中有net_income数据的记录...")
    
    conn = sqlite3.connect('ganggutong_financial_data.db')
    
    try:
        # 查找有net_income数据的记录
        query = """
        SELECT stock_code, report_date, revenue, gross_profit, operating_income, 
               net_income, total_assets
        FROM financial_data
        WHERE net_income IS NOT NULL
        ORDER BY report_date DESC
        """
        
        df = pd.read_sql_query(query, conn)
        print(f"   找到 {len(df)} 条有net_income数据的记录")
        
        if not df.empty:
            print("\n   有net_income数据的记录:")
            print(df.to_string(index=False))
        
        # 检查这些记录的来源
        if not df.empty:
            stock_codes = df['stock_code'].unique()
            print(f"\n   有net_income数据的股票: {list(stock_codes)}")
            
            # 检查下载日志
            for stock_code in stock_codes:
                log_query = """
                SELECT download_time, status, error_message
                FROM download_log
                WHERE stock_code = ? AND data_type = 'financial'
                ORDER BY download_time DESC
                LIMIT 1
                """
                log_df = pd.read_sql_query(log_query, conn, params=(stock_code,))
                if not log_df.empty:
                    print(f"   {stock_code} 最近下载: {log_df.iloc[0]['download_time']} - {log_df.iloc[0]['status']}")
    
    except Exception as e:
        print(f"   数据库查询失败: {e}")
    
    finally:
        conn.close()

def analyze_data_structure():
    """分析数据结构，查找问题"""
    print("\n🔍 分析数据结构...")
    
    conn = sqlite3.connect('ganggutong_financial_data.db')
    
    try:
        # 检查表结构
        cursor = conn.cursor()
        cursor.execute("PRAGMA table_info(financial_data)")
        columns = cursor.fetchall()
        
        print("   financial_data表结构:")
        for col in columns:
            print(f"   {col[1]} {col[2]} {'NOT NULL' if col[3] else 'NULL'}")
        
        # 检查数据分布
        print("\n   数据分布:")
        
        # 按数据源统计
        source_query = """
        SELECT 
            CASE 
                WHEN revenue IS NOT NULL THEN 'has_revenue'
                ELSE 'no_revenue'
            END as revenue_status,
            CASE 
                WHEN net_income IS NOT NULL THEN 'has_net_income'
                ELSE 'no_net_income'
            END as net_income_status,
            COUNT(*) as count
        FROM financial_data
        GROUP BY revenue_status, net_income_status
        """
        
        source_df = pd.read_sql_query(source_query, conn)
        print("   收入和净利润数据分布:")
        print(source_df.to_string(index=False))
        
        # 检查最新的记录
        latest_query = """
        SELECT stock_code, report_date, revenue, net_income, 
               CASE 
                   WHEN revenue IS NOT NULL AND net_income IS NULL THEN 'revenue_only'
                   WHEN revenue IS NULL AND net_income IS NOT NULL THEN 'net_income_only'
                   WHEN revenue IS NOT NULL AND net_income IS NOT NULL THEN 'both'
                   ELSE 'neither'
               END as data_status
        FROM financial_data
        WHERE report_date = (SELECT MAX(report_date) FROM financial_data)
        LIMIT 10
        """
        
        latest_df = pd.read_sql_query(latest_query, conn)
        print(f"\n   最新报告期 ({latest_df['report_date'].iloc[0] if not latest_df.empty else 'N/A'}) 数据状态:")
        if not latest_df.empty:
            status_counts = latest_df['data_status'].value_counts()
            for status, count in status_counts.items():
                print(f"   {status}: {count}")
    
    except Exception as e:
        print(f"   分析失败: {e}")
    
    finally:
        conn.close()

def test_specific_stock():
    """测试特定股票的数据获取"""
    print("\n🔍 测试特定股票的数据获取...")
    
    # 测试腾讯 (00700)
    stock_code = "00700"
    formatted_code = f"{stock_code}.HK"
    
    print(f"   测试股票: {stock_code}")
    
    # 使用与下载器相同的API
    url = f"https://datacenter.eastmoney.com/securities/api/data/v1/get?reportName=RPT_HKF10_FN_INCOME_PC&columns=ALL&quoteColumns=&filter=(SECUCODE%3D%22{formatted_code}%22)&pageNumber=1&pageSize=200&sortTypes=-1&sortColumns=REPORT_DATE&source=F10&client=PC"
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Referer': 'https://datacenter.eastmoney.com/',
        'Accept': 'application/json, text/javascript, */*; q=0.01'
    }
    
    try:
        response = requests.get(url, headers=headers, timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            
            if 'result' in data and 'data' in data['result']:
                records = data['result']['data']
                print(f"   获取到 {len(records)} 条记录")
                
                # 按报告期分组
                date_groups = {}
                for record in records:
                    report_date = record.get('REPORT_DATE', '')[:10]
                    if report_date not in date_groups:
                        date_groups[report_date] = []
                    date_groups[report_date].append(record)
                
                print(f"   报告期数量: {len(date_groups)}")
                
                # 分析每个报告期的数据
                for report_date, items in list(date_groups.items())[:3]:  # 只看前3个报告期
                    print(f"\n   报告期 {report_date}:")
                    
                    revenue_found = False
                    net_income_found = False
                    
                    for item in items:
                        item_name = item.get('STD_ITEM_NAME', '')
                        amount = item.get('AMOUNT')
                        
                        # 检查营收
                        if any(keyword in item_name for keyword in ['营业额', '营运收入', '经营收入总额', '营业收入', '收入总额']):
                            print(f"     营收项目: {item_name} = {amount}")
                            revenue_found = True
                        
                        # 检查净利润
                        if any(keyword in item_name for keyword in ['净利润', '归属于母公司', '归母净利润']):
                            print(f"     净利润项目: {item_name} = {amount}")
                            net_income_found = True
                    
                    print(f"     找到营收: {revenue_found}, 找到净利润: {net_income_found}")
                
            else:
                print("   API响应格式异常")
        else:
            print(f"   API请求失败: {response.status_code}")
    
    except Exception as e:
        print(f"   测试失败: {e}")

def main():
    """主函数"""
    print("🎯 调试net_income空值问题")
    print("=" * 60)
    
    # 1. 测试API
    test_eastmoney_api()
    
    # 2. 检查数据库记录
    check_database_records()
    
    # 3. 分析数据结构
    analyze_data_structure()
    
    # 4. 测试特定股票
    test_specific_stock()
    
    print(f"\n✅ 调试完成 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
