#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细调试下载器方法
"""

import requests
import json
import pandas as pd
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def debug_get_eastmoney_method():
    """详细调试get_eastmoney_financial_data方法"""
    print("🔍 详细调试get_eastmoney_financial_data方法...")
    
    stock_code = "00700"
    formatted_code = f"{stock_code}.HK"
    
    # 模拟下载器中的请求
    url = f"https://datacenter.eastmoney.com/securities/api/data/v1/get?reportName=RPT_HKF10_FN_INCOME_PC&columns=ALL&quoteColumns=&filter=(SECUCODE%3D%22{formatted_code}%22)&pageNumber=1&pageSize=200&sortTypes=-1&sortColumns=REPORT_DATE&source=F10&client=PC"
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        'Referer': 'https://datacenter.eastmoney.com/',
        'Accept': 'application/json, text/javascript, */*; q=0.01'
    }
    
    print(f"   1. 发送请求...")
    response = requests.get(url, headers=headers, timeout=30)
    print(f"      状态码: {response.status_code}")
    
    if response.status_code != 200:
        print(f"      ❌ 请求失败")
        return
    
    print(f"   2. 解析JSON...")
    try:
        data = response.json()
        print(f"      ✅ JSON解析成功")
    except json.JSONDecodeError as e:
        print(f"      ❌ JSON解析失败: {e}")
        return
    
    print(f"   3. 检查响应结构...")
    print(f"      isinstance(data, dict): {isinstance(data, dict)}")
    
    if not isinstance(data, dict):
        print(f"      ❌ 响应不是字典")
        return
    
    print(f"      'result' in data: {'result' in data}")
    
    if 'result' not in data:
        print(f"      ❌ 响应中没有result字段")
        return
    
    result = data['result']
    print(f"      data['result'] is None: {result is None}")
    
    if result is None:
        print(f"      ❌ result字段为None")
        return
    
    print(f"      isinstance(data['result'], dict): {isinstance(result, dict)}")
    
    if not isinstance(result, dict):
        print(f"      ❌ result不是字典")
        return
    
    print(f"      'data' in data['result']: {'data' in result}")
    
    if 'data' not in result:
        print(f"      ❌ result中没有data字段")
        return
    
    result_data = result['data']
    print(f"      data['result']['data'] type: {type(result_data)}")
    print(f"      data['result']['data'] length: {len(result_data) if result_data else 0}")
    print(f"      bool(data['result']['data']): {bool(result_data)}")
    print(f"      not data['result']['data']: {not result_data}")
    
    # 检查完整的条件
    condition = ('result' not in data or
                data['result'] is None or
                not isinstance(data['result'], dict) or
                'data' not in data['result'] or
                not data['result']['data'])
    
    print(f"   4. 完整条件检查:")
    print(f"      'result' not in data: {'result' not in data}")
    print(f"      data['result'] is None: {data['result'] is None}")
    print(f"      not isinstance(data['result'], dict): {not isinstance(data['result'], dict)}")
    print(f"      'data' not in data['result']: {'data' not in data['result']}")
    print(f"      not data['result']['data']: {not data['result']['data']}")
    print(f"      整体条件: {condition}")
    
    if condition:
        print(f"      ❌ 条件为True，会返回'东方财富无财务数据'")
    else:
        print(f"      ✅ 条件为False，会继续处理")
        
        financial_data = data['result']['data']
        print(f"      获取到 {len(financial_data)} 条财务记录")
        
        # 测试process_eastmoney_financial_data
        print(f"   5. 测试数据处理...")
        
        # 模拟处理逻辑
        records = []
        
        # 按报告日期分组
        date_groups = {}
        for item in financial_data:
            report_date = item.get('REPORT_DATE', '')[:10]
            if report_date not in date_groups:
                date_groups[report_date] = []
            date_groups[report_date].append(item)
        
        print(f"      按日期分组: {len(date_groups)} 个报告期")
        
        # 处理每个报告期的数据
        for report_date, items in date_groups.items():
            record = {
                'stock_code': stock_code,
                'report_date': report_date,
                'period_type': 'quarterly'
            }
            
            # 查找营收和净利润数据
            for item in items:
                item_name = item.get('STD_ITEM_NAME', '')
                amount = item.get('AMOUNT')
                
                if amount and pd.notna(amount):
                    try:
                        amount_value = float(amount)
                        
                        # 营收相关项目
                        if any(keyword in item_name for keyword in ['营业额', '营运收入', '经营收入总额', '营业收入', '收入总额']):
                            if 'revenue' not in record or '营业额' in item_name:
                                record['revenue'] = amount_value
                        
                        # 净利润相关项目
                        elif any(keyword in item_name for keyword in ['净利润', '归属于母公司', '归母净利润', '股东应占溢利', '除税后溢利', '持续经营业务税后利润']):
                            if 'net_income' not in record:
                                record['net_income'] = amount_value
                        
                    except (ValueError, TypeError):
                        continue
            
            # 计算利润率
            if record.get('revenue') and record.get('net_income'):
                record['net_margin'] = record['net_income'] / record['revenue']
            
            if len(record) > 3:  # 确保有实际的财务数据
                records.append(record)
        
        print(f"      处理结果: {len(records)} 条记录")
        
        if records:
            first_record = records[0]
            print(f"      第一条记录:")
            print(f"        报告期: {first_record.get('report_date')}")
            print(f"        营收: {first_record.get('revenue', 'N/A'):,} 元" if first_record.get('revenue') else "        营收: N/A")
            print(f"        净利润: {first_record.get('net_income', 'N/A'):,} 元" if first_record.get('net_income') else "        净利润: N/A")

def main():
    """主函数"""
    print("🎯 详细调试下载器方法")
    print("=" * 60)
    
    debug_get_eastmoney_method()
    
    print(f"\n✅ 调试完成")

if __name__ == "__main__":
    main()
