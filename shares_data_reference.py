#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
港股通成分股股本数据参考表
这些数据需要定期更新，建议从专业数据源获取
"""

# 主要港股通成分股的股本数据（单位：股）
# 数据来源：公开财报和交易所公告，截至2024年
SHARES_DATA = {
    # 腾讯控股
    '00700': {
        'shares_outstanding': 9.58e9,  # 约95.8亿股
        'total_shares': 9.58e9,
        'last_updated': '2024-12-31'
    },
    
    # 阿里巴巴-SW
    '09988': {
        'shares_outstanding': 2.07e10,  # 约207亿股
        'total_shares': 2.07e10,
        'last_updated': '2024-12-31'
    },
    
    # 美团-W
    '03690': {
        'shares_outstanding': 6.2e9,   # 约62亿股
        'total_shares': 6.2e9,
        'last_updated': '2024-12-31'
    },
    
    # 建设银行
    '00939': {
        'shares_outstanding': 2.5e11,  # 约2500亿股
        'total_shares': 2.5e11,
        'last_updated': '2024-12-31'
    },
    
    # 工商银行
    '01398': {
        'shares_outstanding': 3.6e11,  # 约3600亿股
        'total_shares': 3.6e11,
        'last_updated': '2024-12-31'
    },
    
    # 中国银行
    '03988': {
        'shares_outstanding': 2.9e11,  # 约2900亿股
        'total_shares': 2.9e11,
        'last_updated': '2024-12-31'
    },
    
    # 汇丰控股
    '00005': {
        'shares_outstanding': 1.9e10,  # 约190亿股
        'total_shares': 1.9e10,
        'last_updated': '2024-12-31'
    },
    
    # 中国移动
    '00941': {
        'shares_outstanding': 2.05e10, # 约205亿股
        'total_shares': 2.05e10,
        'last_updated': '2024-12-31'
    },
    
    # 小米集团-W
    '01810': {
        'shares_outstanding': 2.5e10,  # 约250亿股
        'total_shares': 2.5e10,
        'last_updated': '2024-12-31'
    },
    
    # 比亚迪股份
    '01211': {
        'shares_outstanding': 2.9e9,   # 约29亿股
        'total_shares': 2.9e9,
        'last_updated': '2024-12-31'
    },
}

def get_shares_data(stock_code, report_date=None):
    """
    获取股票的股本数据
    
    Args:
        stock_code: 股票代码
        report_date: 报告期（可选，用于未来扩展）
    
    Returns:
        dict: 包含股本信息的字典，如果没有数据则返回None
    """
    return SHARES_DATA.get(stock_code)

def get_shares_outstanding(stock_code, report_date=None):
    """
    获取流通股本
    
    Args:
        stock_code: 股票代码
        report_date: 报告期（可选）
    
    Returns:
        float: 流通股本数量，如果没有数据则返回None
    """
    data = get_shares_data(stock_code, report_date)
    return data['shares_outstanding'] if data else None

def get_total_shares(stock_code, report_date=None):
    """
    获取总股本
    
    Args:
        stock_code: 股票代码
        report_date: 报告期（可选）
    
    Returns:
        float: 总股本数量，如果没有数据则返回None
    """
    data = get_shares_data(stock_code, report_date)
    return data['total_shares'] if data else None

def list_available_stocks():
    """列出所有有股本数据的股票"""
    return list(SHARES_DATA.keys())

def update_shares_data(stock_code, shares_outstanding, total_shares=None, last_updated=None):
    """
    更新股本数据
    
    Args:
        stock_code: 股票代码
        shares_outstanding: 流通股本
        total_shares: 总股本（可选，默认等于流通股本）
        last_updated: 更新日期（可选，默认为当前日期）
    """
    if last_updated is None:
        from datetime import datetime
        last_updated = datetime.now().strftime('%Y-%m-%d')
    
    if total_shares is None:
        total_shares = shares_outstanding
    
    SHARES_DATA[stock_code] = {
        'shares_outstanding': shares_outstanding,
        'total_shares': total_shares,
        'last_updated': last_updated
    }

if __name__ == "__main__":
    # 测试功能
    print("港股通成分股股本数据:")
    print("=" * 50)
    
    for stock_code in sorted(SHARES_DATA.keys()):
        data = get_shares_data(stock_code)
        print(f"{stock_code}: 流通股本 {data['shares_outstanding']/1e9:.1f}十亿股, "
              f"更新日期 {data['last_updated']}")
    
    print(f"\n总共有 {len(SHARES_DATA)} 只股票的股本数据")
    
    # 测试腾讯的数据
    tencent_shares = get_shares_outstanding('00700')
    print(f"\n腾讯控股流通股本: {tencent_shares/1e9:.2f} 十亿股")
