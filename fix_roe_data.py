#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复ROE异常数据脚本
"""

import sqlite3
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def fix_abnormal_roe(db_name='ganggutong_financial_data.db'):
    """修复异常的ROE数据"""
    
    conn = sqlite3.connect(db_name)
    cursor = conn.cursor()
    
    # 查找异常ROE数据
    cursor.execute("""
        SELECT id, stock_code, report_date, roe, net_income, shareholders_equity
        FROM financial_data 
        WHERE roe IS NOT NULL AND (roe < -1 OR roe > 1)
    """)
    
    abnormal_records = cursor.fetchall()
    logger.info(f"发现 {len(abnormal_records)} 条异常ROE数据")
    
    fixed_count = 0
    removed_count = 0
    
    for record in abnormal_records:
        record_id, stock_code, report_date, roe, net_income, shareholders_equity = record
        
        # 检查数据质量
        if shareholders_equity is None or net_income is None:
            # 如果缺少关键数据，将ROE设为NULL
            cursor.execute("UPDATE financial_data SET roe = NULL WHERE id = ?", (record_id,))
            removed_count += 1
            logger.debug(f"移除 {stock_code} {report_date} 的ROE (缺少数据)")
            continue
        
        # 检查股东权益是否过小（可能导致异常ROE）
        if abs(shareholders_equity) < 1000000:  # 小于100万
            cursor.execute("UPDATE financial_data SET roe = NULL WHERE id = ?", (record_id,))
            removed_count += 1
            logger.debug(f"移除 {stock_code} {report_date} 的ROE (股东权益过小: {shareholders_equity})")
            continue
        
        # 重新计算ROE
        new_roe = net_income / shareholders_equity
        
        # 如果重新计算后仍然异常，设为NULL
        if new_roe < -1 or new_roe > 1:
            cursor.execute("UPDATE financial_data SET roe = NULL WHERE id = ?", (record_id,))
            removed_count += 1
            logger.debug(f"移除 {stock_code} {report_date} 的ROE (重算后仍异常: {new_roe:.4f})")
        else:
            # 更新为正确的ROE值
            cursor.execute("UPDATE financial_data SET roe = ? WHERE id = ?", (new_roe, record_id))
            fixed_count += 1
            logger.debug(f"修复 {stock_code} {report_date} 的ROE: {roe:.4f} -> {new_roe:.4f}")
    
    conn.commit()
    conn.close()
    
    logger.info(f"ROE数据修复完成:")
    logger.info(f"  修复: {fixed_count} 条")
    logger.info(f"  移除: {removed_count} 条")
    logger.info(f"  总计: {fixed_count + removed_count} 条")

def recalculate_all_roe(db_name='ganggutong_financial_data.db'):
    """重新计算所有ROE数据"""
    
    conn = sqlite3.connect(db_name)
    cursor = conn.cursor()
    
    # 获取有净利润和股东权益但ROE为空的记录
    cursor.execute("""
        SELECT id, stock_code, report_date, net_income, shareholders_equity
        FROM financial_data 
        WHERE net_income IS NOT NULL 
          AND shareholders_equity IS NOT NULL 
          AND roe IS NULL
          AND ABS(shareholders_equity) >= 1000000
    """)
    
    missing_roe_records = cursor.fetchall()
    logger.info(f"发现 {len(missing_roe_records)} 条可以计算ROE的记录")
    
    calculated_count = 0
    
    for record in missing_roe_records:
        record_id, stock_code, report_date, net_income, shareholders_equity = record
        
        # 计算ROE
        roe = net_income / shareholders_equity
        
        # 只保存合理范围内的ROE
        if -1 <= roe <= 1:
            cursor.execute("UPDATE financial_data SET roe = ? WHERE id = ?", (roe, record_id))
            calculated_count += 1
            logger.debug(f"计算 {stock_code} {report_date} 的ROE: {roe:.4f}")
    
    conn.commit()
    conn.close()
    
    logger.info(f"新计算ROE: {calculated_count} 条")

def analyze_roe_after_fix(db_name='ganggutong_financial_data.db'):
    """分析修复后的ROE数据"""
    
    conn = sqlite3.connect(db_name)
    
    query = """
    SELECT 
        COUNT(*) as total_records,
        COUNT(roe) as has_roe,
        ROUND((COUNT(roe) * 100.0 / COUNT(*)), 2) as roe_fill_rate,
        COUNT(CASE WHEN roe IS NOT NULL AND roe BETWEEN -1 AND 1 THEN 1 END) as reasonable_roe,
        COUNT(CASE WHEN roe IS NOT NULL AND (roe < -1 OR roe > 1) THEN 1 END) as abnormal_roe
    FROM financial_data
    """
    
    result = conn.execute(query).fetchone()
    
    logger.info("=== 修复后ROE数据统计 ===")
    logger.info(f"总记录数: {result[0]}")
    logger.info(f"有ROE数据: {result[1]} ({result[2]}%)")
    logger.info(f"合理ROE: {result[3]}")
    logger.info(f"异常ROE: {result[4]}")
    
    conn.close()

if __name__ == "__main__":
    logger.info("开始修复ROE数据...")
    
    # 1. 修复异常ROE数据
    fix_abnormal_roe()
    
    # 2. 重新计算缺失的ROE数据
    recalculate_all_roe()
    
    # 3. 分析修复结果
    analyze_roe_after_fix()
    
    logger.info("ROE数据修复完成！")
