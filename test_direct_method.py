#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接测试下载器方法
"""

import requests
import json
import pandas as pd
import time
import random

def test_direct_method():
    """直接复制下载器中的方法逻辑"""
    print("🔍 直接测试下载器方法逻辑...")
    
    stock_code = "00700"
    stock_name = "腾讯控股"
    
    try:
        # 格式化股票代码
        formatted_code = f"{stock_code}.HK"
        
        print(f"   股票代码: {stock_code}")
        print(f"   格式化代码: {formatted_code}")

        # 构建API URL - 使用利润表接口（基于成功的实现）
        url = f"https://datacenter.eastmoney.com/securities/api/data/v1/get?reportName=RPT_HKF10_FN_INCOME_PC&columns=ALL&quoteColumns=&filter=(SECUCODE%3D%22{formatted_code}%22)&pageNumber=1&pageSize=200&sortTypes=-1&sortColumns=REPORT_DATE&source=F10&client=PC"
        
        print(f"   URL: {url}")

        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Referer': 'https://datacenter.eastmoney.com/',
            'Accept': 'application/json, text/javascript, */*; q=0.01',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
        }

        # 添加随机延迟避免被识别为爬虫
        time.sleep(random.uniform(1, 3))
        
        # 使用会话保持连接
        session = requests.Session()
        session.headers.update(headers)
        response = session.get(url, timeout=30)

        print(f"   状态码: {response.status_code}")

        if response.status_code != 200:
            print(f"   ❌ 请求失败: 状态码 {response.status_code}")
            return []

        try:
            data = response.json()
            print(f"   ✅ JSON解析成功")
        except json.JSONDecodeError as e:
            print(f"   ❌ JSON解析失败: {e}")
            return []

        # 检查响应结构
        if not isinstance(data, dict):
            print(f"   ❌ 响应不是字典")
            return []

        # 详细调试信息
        print(f"   完整响应: {json.dumps(data, ensure_ascii=False)[:200]}...")
        print(f"   'result' in data: {'result' in data}")
        
        if 'result' in data:
            print(f"   data['result'] is None: {data['result'] is None}")
            if data['result'] is not None:
                print(f"   isinstance(data['result'], dict): {isinstance(data['result'], dict)}")
                if isinstance(data['result'], dict):
                    print(f"   'data' in data['result']: {'data' in data['result']}")
                    if 'data' in data['result']:
                        print(f"   len(data['result']['data']): {len(data['result']['data']) if data['result']['data'] else 0}")
                        print(f"   bool(data['result']['data']): {bool(data['result']['data'])}")

        if ('result' not in data or
            data['result'] is None or
            not isinstance(data['result'], dict) or
            'data' not in data['result'] or
            not data['result']['data']):
            print(f"   ❌ 无财务数据")
            return []

        financial_data = data['result']['data']
        print(f"   ✅ 获取到 {len(financial_data)} 条财务记录")

        # 处理数据
        records = []

        # 按报告日期分组
        date_groups = {}
        for item in financial_data:
            report_date = item.get('REPORT_DATE', '')[:10]
            if report_date not in date_groups:
                date_groups[report_date] = []
            date_groups[report_date].append(item)

        print(f"   按日期分组: {len(date_groups)} 个报告期")

        # 处理每个报告期的数据
        for report_date, items in date_groups.items():
            record = {
                'stock_code': stock_code,
                'report_date': report_date,
                'period_type': 'quarterly'
            }

            # 查找营收数据
            for item in items:
                item_name = item.get('STD_ITEM_NAME', '')
                amount = item.get('AMOUNT')

                if amount and pd.notna(amount):
                    try:
                        amount_value = float(amount)

                        # 营收相关项目
                        if any(keyword in item_name for keyword in ['营业额', '营运收入', '经营收入总额', '营业收入', '收入总额']):
                            if 'revenue' not in record or '营业额' in item_name:  # 优先使用营业额
                                record['revenue'] = amount_value  # API返回的已经是元

                        # 净利润相关项目 (港股使用"股东应占溢利"等术语)
                        elif any(keyword in item_name for keyword in ['净利润', '归属于母公司', '归母净利润', '股东应占溢利', '除税后溢利', '持续经营业务税后利润']):
                            if 'net_income' not in record:
                                record['net_income'] = amount_value  # API返回的已经是元

                    except (ValueError, TypeError):
                        continue

            # 计算利润率
            if record.get('revenue') and record.get('net_income'):
                record['net_margin'] = record['net_income'] / record['revenue']

            if len(record) > 3:  # 确保有实际的财务数据
                records.append(record)

        print(f"   ✅ 处理完成: {len(records)} 条记录")
        
        if records:
            first_record = records[0]
            print(f"   第一条记录:")
            print(f"     报告期: {first_record.get('report_date')}")
            print(f"     营收: {first_record.get('revenue', 'N/A'):,} 元" if first_record.get('revenue') else "     营收: N/A")
            print(f"     净利润: {first_record.get('net_income', 'N/A'):,} 元" if first_record.get('net_income') else "     净利润: N/A")

        return records

    except Exception as e:
        print(f"   ❌ 获取财务数据失败: {e}")
        import traceback
        traceback.print_exc()
        return []

def main():
    """主函数"""
    print("🎯 直接测试下载器方法")
    print("=" * 60)
    
    result = test_direct_method()
    
    print(f"\n📊 最终结果: {len(result) if result else 0} 条记录")
    
    print(f"\n✅ 测试完成")

if __name__ == "__main__":
    main()
